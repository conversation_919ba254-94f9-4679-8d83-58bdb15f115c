"""
Test the duplicate prevention and incremental processing fixes
"""

import time
import logging
import json
import os
import threading
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_processing_locks():
    """Test the processing locks mechanism"""
    logger.info("Testing processing locks mechanism...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test if processing locks are initialized
        if hasattr(crawler, 'processing_posts') and hasattr(crawler, 'processing_lock'):
            logger.info("✅ Processing locks initialized")
            
            # Test lock functionality
            test_url = "https://lowendtalk.com/discussion/test/lock"
            
            # Simulate adding a post to processing
            with crawler.processing_lock:
                crawler.processing_posts.add(test_url)
                logger.info(f"✅ Added post to processing set: {len(crawler.processing_posts)} posts")
            
            # Test checking if post is being processed
            with crawler.processing_lock:
                is_processing = test_url in crawler.processing_posts
                logger.info(f"✅ Post processing check: {is_processing}")
            
            # Test removing from processing
            with crawler.processing_lock:
                crawler.processing_posts.discard(test_url)
                logger.info(f"✅ Removed post from processing set: {len(crawler.processing_posts)} posts")
            
            return True
        else:
            logger.error("❌ Processing locks not initialized")
            return False
            
    except Exception as e:
        logger.error(f"Processing locks test failed: {e}")
        return False

def test_state_comparison_logic():
    """Test the state comparison logic"""
    logger.info("Testing state comparison logic...")
    
    try:
        # Mock state data
        prev_state = {
            "last_comment_count": 50,
            "last_comment_content": "This is the last comment",
            "processed_comment_ids": ["comment_1", "comment_2", "comment_3"]
        }
        
        # Test cases
        test_cases = [
            # (current_count, should_process, description)
            (50, False, "Same comment count should not be processed"),
            (49, False, "Decreased comment count should not be processed"),
            (51, True, "Increased comment count should be processed"),
            (55, True, "Significantly increased comment count should be processed")
        ]
        
        for current_count, expected, description in test_cases:
            # Simulate the comparison logic
            should_process = current_count > prev_state["last_comment_count"]
            
            if should_process == expected:
                logger.info(f"✅ {description}: {should_process}")
            else:
                logger.error(f"❌ {description}: Expected {expected}, got {should_process}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"State comparison test failed: {e}")
        return False

def test_incremental_processing_logic():
    """Test the incremental processing logic"""
    logger.info("Testing incremental processing logic...")
    
    try:
        # Mock comment data (newest to oldest)
        mock_comments = [
            {"text": "This is the newest comment", "id": "comment_5"},
            {"text": "This is comment 4", "id": "comment_4"},
            {"text": "This is comment 3", "id": "comment_3"},
            {"text": "This is the last known comment", "id": "comment_2"},  # Last known
            {"text": "This is comment 1", "id": "comment_1"},
        ]
        
        # Simulate incremental processing
        last_known_content = "This is the last known comment"
        prev_processed_ids = {"comment_1", "comment_2"}
        
        new_comments_processed = 0
        new_processed_ids = set()
        last_comment_content_found = ""
        
        logger.info("Simulating incremental processing...")
        logger.info(f"Last known content: {last_known_content}")
        logger.info(f"Previously processed IDs: {prev_processed_ids}")
        
        for i, comment in enumerate(mock_comments):
            comment_text = comment["text"]
            comment_id = comment["id"]
            
            logger.info(f"  Processing comment {i+1}: {comment_id}")
            
            # Store first (newest) comment content
            if not last_comment_content_found:
                last_comment_content_found = comment_text[:200]
                logger.info(f"  -> Stored as new last comment content")
            
            # Stop when reaching last known comment
            if comment_text[:200] == last_known_content:
                logger.info(f"  -> Found last known comment, stopping")
                break
            
            # Skip if already processed
            if comment_id in prev_processed_ids:
                logger.info(f"  -> Already processed, skipping")
                continue
            
            # This is a new comment
            new_comments_processed += 1
            new_processed_ids.add(comment_id)
            logger.info(f"  -> NEW comment detected ({new_comments_processed})")
        
        # Verify results
        expected_new_comments = 2  # comment_5 and comment_4
        expected_new_ids = {"comment_5", "comment_4"}
        expected_last_content = "This is the newest comment"
        
        if (new_comments_processed == expected_new_comments and 
            new_processed_ids == expected_new_ids and
            last_comment_content_found[:200] == expected_last_content):
            logger.info("✅ Incremental processing logic PASSED!")
            logger.info(f"  New comments processed: {new_comments_processed}")
            logger.info(f"  New processed IDs: {new_processed_ids}")
            logger.info(f"  New last comment: {last_comment_content_found[:50]}...")
            return True
        else:
            logger.error("❌ Incremental processing logic FAILED!")
            logger.error(f"  Expected new comments: {expected_new_comments}, got: {new_comments_processed}")
            logger.error(f"  Expected new IDs: {expected_new_ids}, got: {new_processed_ids}")
            return False
            
    except Exception as e:
        logger.error(f"Incremental processing test failed: {e}")
        return False

def test_comprehensive_state_update():
    """Test the comprehensive state update method"""
    logger.info("Testing comprehensive state update...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test data
        test_url = "https://lowendtalk.com/discussion/test/comprehensive"
        test_comment_count = 75
        test_datetime = datetime.now().isoformat()
        test_processed_ids = {"comment_1", "comment_2", "comment_3", "comment_4"}
        test_last_content = "This is the comprehensive test last comment content."
        test_new_comments = 3
        test_flash_sales = 1
        
        logger.info(f"Testing comprehensive state update with:")
        logger.info(f"  URL: {test_url}")
        logger.info(f"  Comment count: {test_comment_count}")
        logger.info(f"  New comments: {test_new_comments}")
        logger.info(f"  Flash sales: {test_flash_sales}")
        
        # Update state
        crawler._update_processed_post_state_comprehensive(
            test_url,
            test_comment_count,
            test_datetime,
            test_processed_ids,
            test_last_content,
            test_new_comments,
            test_flash_sales
        )
        
        # Verify state was updated
        time.sleep(1)
        current_state = crawler.state_manager.load_state()
        processed_posts = current_state.get("processed_posts", {})
        
        if test_url in processed_posts:
            saved_data = processed_posts[test_url]
            
            # Check all fields
            checks = [
                (saved_data.get("last_comment_count"), test_comment_count, "comment count"),
                (saved_data.get("last_comment_content"), test_last_content, "last comment content"),
                (saved_data.get("new_comments_in_last_run"), test_new_comments, "new comments count"),
                (saved_data.get("flash_sales_in_last_run"), test_flash_sales, "flash sales count"),
                (saved_data.get("status"), "processed", "status"),
                (saved_data.get("processing_method"), "incremental", "processing method")
            ]
            
            all_passed = True
            for actual, expected, field_name in checks:
                if actual == expected:
                    logger.info(f"✅ {field_name}: {actual}")
                else:
                    logger.error(f"❌ {field_name}: Expected {expected}, got {actual}")
                    all_passed = False
            
            if all_passed:
                logger.info("✅ Comprehensive state update PASSED!")
                return True
            else:
                logger.error("❌ Comprehensive state update FAILED!")
                return False
        else:
            logger.error("❌ Comprehensive state update FAILED! Test URL not found in state")
            return False
            
    except Exception as e:
        logger.error(f"Comprehensive state update test failed: {e}")
        return False

def test_concurrent_processing_prevention():
    """Test concurrent processing prevention"""
    logger.info("Testing concurrent processing prevention...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        import threading
        import time
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        test_url = "https://lowendtalk.com/discussion/test/concurrent"
        results = []
        
        def worker_simulation(worker_id):
            """Simulate a worker trying to process the same post"""
            try:
                # Check if post is already being processed
                with crawler.processing_lock:
                    if test_url in crawler.processing_posts:
                        results.append(f"Worker {worker_id}: Post already being processed")
                        return
                    
                    # Mark as being processed
                    crawler.processing_posts.add(test_url)
                    results.append(f"Worker {worker_id}: Started processing")
                
                # Simulate processing time
                time.sleep(2)
                
                # Finish processing
                with crawler.processing_lock:
                    crawler.processing_posts.discard(test_url)
                    results.append(f"Worker {worker_id}: Finished processing")
                    
            except Exception as e:
                results.append(f"Worker {worker_id}: Error - {e}")
        
        # Start multiple workers simultaneously
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker_simulation, args=(i+1,))
            threads.append(thread)
        
        # Start all threads at the same time
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Analyze results
        logger.info("Concurrent processing test results:")
        for result in results:
            logger.info(f"  {result}")
        
        # Should have exactly one "Started processing" and one "Finished processing"
        started_count = sum(1 for r in results if "Started processing" in r)
        finished_count = sum(1 for r in results if "Finished processing" in r)
        already_processing_count = sum(1 for r in results if "already being processed" in r)
        
        if started_count == 1 and finished_count == 1 and already_processing_count == 2:
            logger.info("✅ Concurrent processing prevention PASSED!")
            logger.info(f"  Only 1 worker processed, 2 workers were blocked")
            return True
        else:
            logger.error("❌ Concurrent processing prevention FAILED!")
            logger.error(f"  Started: {started_count}, Finished: {finished_count}, Blocked: {already_processing_count}")
            return False
            
    except Exception as e:
        logger.error(f"Concurrent processing prevention test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Duplicate Prevention and Incremental Processing Fixes")
    logger.info("=" * 70)
    
    # Test 1: Processing locks
    logger.info("Test 1: Processing locks mechanism")
    locks_test = test_processing_locks()
    
    # Test 2: State comparison logic
    logger.info("\nTest 2: State comparison logic")
    comparison_test = test_state_comparison_logic()
    
    # Test 3: Incremental processing logic
    logger.info("\nTest 3: Incremental processing logic")
    incremental_test = test_incremental_processing_logic()
    
    # Test 4: Comprehensive state update
    logger.info("\nTest 4: Comprehensive state update")
    state_update_test = test_comprehensive_state_update()
    
    # Test 5: Concurrent processing prevention
    logger.info("\nTest 5: Concurrent processing prevention")
    concurrent_test = test_concurrent_processing_prevention()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 40)
    logger.info(f"Processing locks: {'PASS' if locks_test else 'FAIL'}")
    logger.info(f"State comparison: {'PASS' if comparison_test else 'FAIL'}")
    logger.info(f"Incremental processing: {'PASS' if incremental_test else 'FAIL'}")
    logger.info(f"Comprehensive state update: {'PASS' if state_update_test else 'FAIL'}")
    logger.info(f"Concurrent prevention: {'PASS' if concurrent_test else 'FAIL'}")
    
    total_passed = sum([locks_test, comparison_test, incremental_test, state_update_test, concurrent_test])
    
    if total_passed == 5:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("Duplicate prevention and incremental processing fixes are working correctly:")
        logger.info("  ✅ No duplicate processing of same posts by multiple workers")
        logger.info("  ✅ Accurate comment count comparison between JSON and monitor")
        logger.info("  ✅ True incremental processing (only new comments)")
        logger.info("  ✅ Comprehensive state updates with verification")
        logger.info("  ✅ Thread-safe concurrent processing prevention")
        logger.info("\nThe crawler will now:")
        logger.info("  - Avoid processing the same post multiple times")
        logger.info("  - Only process posts when comment count actually increases")
        logger.info("  - Process only new comments since last run")
        logger.info("  - Save accurate comment counts to JSON")
        logger.info("  - Store last comment content for next incremental run")
    elif total_passed > 0:
        logger.info(f"\n⚠️ {total_passed}/5 tests passed.")
        logger.info("Some functionality is working, but issues remain.")
    else:
        logger.error("\n💥 ALL TESTS FAILED!")
        logger.error("Duplicate prevention fixes need further debugging.")
    
    return total_passed == 5

if __name__ == "__main__":
    main()
