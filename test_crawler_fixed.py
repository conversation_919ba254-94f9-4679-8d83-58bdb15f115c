"""
Test the fixed crawler without emoji characters
"""

import time
import logging
import sys
import os

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Setup logging without emoji
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('test_crawler.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_crawler_basic():
    """Test basic crawler functionality"""
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        logger.info("Creating crawler configuration...")
        config = CrawlerConfig(
            enable_pagination=True,
            max_pages_to_check=2,
            comments_per_page=25,
            headless=False,
            refresh_interval=5,  # Slower for testing
            num_workers=1,  # Single worker for testing
            max_posts_to_check=2  # Limit posts for testing
        )
        
        logger.info("Configuration created successfully")
        logger.info(f"Pagination enabled: {config.enable_pagination}")
        logger.info(f"Max pages to check: {config.max_pages_to_check}")
        
        # Create crawler
        logger.info("Creating crawler instance...")
        crawler = ForumCrawler(config)
        
        # Test browser creation
        logger.info("Testing browser creation...")
        crawler.browser_manager.start_browser()
        
        # Test tab creation
        logger.info("Testing tab creation...")
        test_tab = crawler.browser_manager.create_tab()
        
        if test_tab:
            logger.info("Tab created successfully")
            
            # Test basic navigation
            logger.info("Testing basic navigation...")
            test_tab.get("https://lowendtalk.com")
            time.sleep(3)
            
            title = test_tab.title
            logger.info(f"Page title: {title}")
            
            if "lowendtalk" in title.lower():
                logger.info("Navigation test PASSED")
                return True
            else:
                logger.error("Navigation test FAILED")
                return False
        else:
            logger.error("Tab creation FAILED")
            return False
            
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False
    finally:
        try:
            if 'crawler' in locals():
                crawler.browser_manager.cleanup()
                logger.info("Browser cleaned up")
        except:
            pass

def test_pagination_logic():
    """Test pagination logic specifically"""
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler, BrowserManager
        
        logger.info("Testing pagination logic...")
        
        config = CrawlerConfig(
            enable_pagination=True,
            max_pages_to_check=2,
            comments_per_page=25,
            headless=False
        )
        
        crawler = ForumCrawler(config)
        browser_manager = BrowserManager(config)
        browser_manager.start_browser()
        
        test_page = browser_manager.create_tab()
        if not test_page:
            logger.error("Failed to create test page")
            return False
        
        # Test with a simple post
        test_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        logger.info(f"Testing pagination with: {test_url}")
        
        test_page.get(test_url)
        time.sleep(3)
        
        # Handle Cloudflare
        browser_manager.handle_cloudflare_verification(test_page, max_wait_time=20)
        
        # Count comments
        comments = test_page.eles('.Comment')
        logger.info(f"Found {len(comments)} comments")
        
        # Test pagination method
        worker_id = "TEST"
        all_comments = crawler._get_all_comments_with_pagination(test_page, worker_id, len(comments) + 5)
        
        if all_comments:
            logger.info(f"Pagination test PASSED: {len(all_comments)} comments collected")
            return True
        else:
            logger.error("Pagination test FAILED: No comments collected")
            return False
            
    except Exception as e:
        logger.error(f"Pagination test failed: {e}")
        return False
    finally:
        try:
            if 'browser_manager' in locals():
                browser_manager.cleanup()
        except:
            pass

def test_worker_stability():
    """Test worker thread stability"""
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        logger.info("Testing worker stability...")
        
        config = CrawlerConfig(
            enable_pagination=True,
            max_pages_to_check=2,
            comments_per_page=25,
            headless=False,
            refresh_interval=10,  # Slower for stability
            num_workers=1,
            max_posts_to_check=1
        )
        
        crawler = ForumCrawler(config)
        
        # Start crawler for a short time
        logger.info("Starting crawler for stability test...")
        
        # This will run the crawler briefly
        import threading
        import time
        
        def run_crawler():
            try:
                crawler.run()
            except Exception as e:
                logger.error(f"Crawler error: {e}")
        
        # Start crawler in background
        crawler_thread = threading.Thread(target=run_crawler)
        crawler_thread.daemon = True
        crawler_thread.start()
        
        # Let it run for 30 seconds
        logger.info("Letting crawler run for 30 seconds...")
        time.sleep(30)
        
        # Stop crawler
        logger.info("Stopping crawler...")
        crawler.stop()
        
        # Wait for thread to finish
        crawler_thread.join(timeout=10)
        
        logger.info("Worker stability test completed")
        return True
        
    except Exception as e:
        logger.error(f"Worker stability test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Starting Crawler Tests (No Emoji Version)")
    logger.info("=" * 50)
    
    # Test 1: Basic functionality
    logger.info("Test 1: Basic crawler functionality")
    basic_test = test_crawler_basic()
    
    # Test 2: Pagination logic
    logger.info("\nTest 2: Pagination logic")
    pagination_test = test_pagination_logic()
    
    # Test 3: Worker stability
    logger.info("\nTest 3: Worker stability")
    stability_test = test_worker_stability()
    
    # Summary
    logger.info("\nTest Results Summary:")
    logger.info(f"Basic functionality: {'PASS' if basic_test else 'FAIL'}")
    logger.info(f"Pagination logic: {'PASS' if pagination_test else 'FAIL'}")
    logger.info(f"Worker stability: {'PASS' if stability_test else 'FAIL'}")
    
    total_passed = sum([basic_test, pagination_test, stability_test])
    
    if total_passed == 3:
        logger.info("\nALL TESTS PASSED!")
        logger.info("The crawler is working correctly without emoji issues.")
    elif total_passed > 0:
        logger.info(f"\n{total_passed}/3 tests passed.")
        logger.info("Some functionality is working.")
    else:
        logger.error("\nALL TESTS FAILED!")
        logger.error("The crawler needs further debugging.")
    
    return total_passed > 0

if __name__ == "__main__":
    main()
