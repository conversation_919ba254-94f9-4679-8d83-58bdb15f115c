"""
Test the crawler hang fix
"""

import time
import logging
import threading

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_browser_configuration():
    """Test if browser configuration works"""
    logger.info("Testing browser configuration...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, BrowserManager
        
        config = CrawlerConfig(headless=True)
        browser_manager = BrowserManager(config)
        
        # Test browser startup
        browser_manager.start_browser()
        logger.info("✅ Browser started successfully")
        
        # Test tab creation
        test_page = browser_manager.create_tab()
        if test_page:
            logger.info("✅ Tab created successfully")
            
            # Test navigation
            test_page.get("https://lowendtalk.com")
            time.sleep(3)
            
            current_url = test_page.url
            logger.info(f"✅ Navigation successful: {current_url}")
            
            test_page.close()
        else:
            logger.error("❌ Failed to create tab")
            return False
        
        browser_manager.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"Browser configuration test failed: {e}")
        return False

def test_page_extraction():
    """Test page extraction with timeout"""
    logger.info("Testing page extraction...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler, BrowserManager
        
        config = CrawlerConfig(headless=True)
        crawler = ForumCrawler(config)
        browser_manager = BrowserManager(config)
        
        browser_manager.start_browser()
        test_page = browser_manager.create_tab()
        
        if not test_page:
            logger.error("Failed to create test page")
            return False
        
        # Navigate to forum
        logger.info("Navigating to forum...")
        test_page.get("https://lowendtalk.com/categories/offers")
        time.sleep(5)
        
        # Check URL
        current_url = test_page.url
        logger.info(f"Current URL: {current_url}")
        
        if "lowendtalk.com" not in current_url:
            logger.error(f"Navigation failed: {current_url}")
            return False
        
        # Test extraction with timeout
        logger.info("Testing post extraction...")
        
        def extraction_with_timeout():
            try:
                posts = crawler._extract_posts_from_page(test_page)
                logger.info(f"Extracted {len(posts)} posts")
                return len(posts) > 0
            except Exception as e:
                logger.error(f"Extraction error: {e}")
                return False
        
        # Run extraction in thread with timeout
        result = [False]
        
        def run_extraction():
            result[0] = extraction_with_timeout()
        
        extraction_thread = threading.Thread(target=run_extraction)
        extraction_thread.start()
        extraction_thread.join(timeout=30)  # 30 second timeout
        
        if extraction_thread.is_alive():
            logger.error("❌ Extraction timed out after 30 seconds")
            return False
        elif result[0]:
            logger.info("✅ Extraction completed successfully")
            return True
        else:
            logger.error("❌ Extraction failed")
            return False
        
    except Exception as e:
        logger.error(f"Page extraction test failed: {e}")
        return False
    finally:
        try:
            browser_manager.cleanup()
        except:
            pass

def test_monitoring_configuration():
    """Test monitoring configuration"""
    logger.info("Testing monitoring configuration...")
    
    try:
        from improved_forum_crawler import CrawlerConfig
        
        config = CrawlerConfig.from_file("crawler_config.json")
        
        # Check monitor interval
        if hasattr(config, 'monitor_interval'):
            interval = config.monitor_interval
            logger.info(f"Monitor interval: {interval} seconds")
            
            if interval >= 30:
                logger.info("✅ Monitor interval is reasonable")
                return True
            else:
                logger.warning(f"⚠️ Monitor interval too short: {interval}s")
                return False
        else:
            logger.error("❌ Monitor interval not configured")
            return False
            
    except Exception as e:
        logger.error(f"Monitoring configuration test failed: {e}")
        return False

def test_real_time_startup():
    """Test real-time monitoring startup without hanging"""
    logger.info("Testing real-time monitoring startup...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(
            monitor_interval=60,  # Long interval for testing
            num_workers=1,
            headless=True
        )
        
        crawler = ForumCrawler(config)
        
        # Test startup in thread with timeout
        startup_result = [False]
        startup_error = [None]
        
        def test_startup():
            try:
                # Initialize browser manager
                crawler.browser_manager = crawler.BrowserManager(config)
                crawler.browser_manager.start_browser()
                
                # Test monitoring thread creation
                crawler.monitoring_active = True
                
                startup_result[0] = True
                logger.info("✅ Startup components initialized")
                
            except Exception as e:
                startup_error[0] = str(e)
                logger.error(f"Startup error: {e}")
            finally:
                try:
                    if hasattr(crawler, 'browser_manager') and crawler.browser_manager:
                        crawler.browser_manager.cleanup()
                except:
                    pass
        
        startup_thread = threading.Thread(target=test_startup)
        startup_thread.start()
        startup_thread.join(timeout=20)  # 20 second timeout
        
        if startup_thread.is_alive():
            logger.error("❌ Startup timed out after 20 seconds")
            return False
        elif startup_result[0]:
            logger.info("✅ Real-time monitoring startup successful")
            return True
        else:
            logger.error(f"❌ Startup failed: {startup_error[0]}")
            return False
            
    except Exception as e:
        logger.error(f"Real-time startup test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Crawler Hang Fix")
    logger.info("=" * 40)
    
    # Test 1: Browser configuration
    logger.info("Test 1: Browser configuration")
    browser_test = test_browser_configuration()
    
    # Test 2: Monitoring configuration
    logger.info("\nTest 2: Monitoring configuration")
    config_test = test_monitoring_configuration()
    
    # Test 3: Page extraction
    logger.info("\nTest 3: Page extraction with timeout")
    extraction_test = test_page_extraction()
    
    # Test 4: Real-time startup
    logger.info("\nTest 4: Real-time monitoring startup")
    startup_test = test_real_time_startup()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 30)
    logger.info(f"Browser configuration: {'PASS' if browser_test else 'FAIL'}")
    logger.info(f"Monitoring configuration: {'PASS' if config_test else 'FAIL'}")
    logger.info(f"Page extraction: {'PASS' if extraction_test else 'FAIL'}")
    logger.info(f"Real-time startup: {'PASS' if startup_test else 'FAIL'}")
    
    total_passed = sum([browser_test, config_test, extraction_test, startup_test])
    
    if total_passed == 4:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("Crawler hang fix is working correctly.")
        logger.info("The crawler should now run without hanging.")
        logger.info("\nReady to run: python improved_forum_crawler.py")
    elif total_passed > 0:
        logger.info(f"\n⚠️ {total_passed}/4 tests passed.")
        logger.info("Some functionality is working, but issues remain.")
    else:
        logger.error("\n💥 ALL TESTS FAILED!")
        logger.error("Crawler hang fix needs further debugging.")
    
    return total_passed == 4

if __name__ == "__main__":
    main()
