"""
Test the refactored real-time forum crawler
"""

import time
import logging
import json
import os
import threading
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_configuration_loading():
    """Test loading of new configuration options"""
    logger.info("Testing configuration loading...")
    
    try:
        from improved_forum_crawler import CrawlerConfig
        
        config = CrawlerConfig.from_file("crawler_config.json")
        
        required_attrs = [
            'min_comments_for_new_posts',
            'monitor_interval',
            'num_workers'
        ]
        
        for attr in required_attrs:
            if hasattr(config, attr):
                value = getattr(config, attr)
                logger.info(f"✅ {attr}: {value}")
            else:
                logger.error(f"❌ Missing configuration: {attr}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration loading test failed: {e}")
        return False

def test_change_detection_logic():
    """Test the change detection logic"""
    logger.info("Testing change detection logic...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(min_comments_for_new_posts=5)
        crawler = ForumCrawler(config)
        
        # Test cases for change detection
        test_cases = [
            # (post_data, processed_posts, expected_change_type)
            (
                {"post_url": "https://test.com/new", "current_comment_count": 10},
                {},
                "new_post"
            ),
            (
                {"post_url": "https://test.com/existing", "current_comment_count": 25},
                {"https://test.com/existing": {"last_comment_count": 20}},
                "comment_increase"
            ),
            (
                {"post_url": "https://test.com/same", "current_comment_count": 15},
                {"https://test.com/same": {"last_comment_count": 15}},
                "no_change"
            ),
            (
                {"post_url": "https://test.com/decreased", "current_comment_count": 10},
                {"https://test.com/decreased": {"last_comment_count": 15}},
                "comment_decrease"
            )
        ]
        
        for post_data, processed_posts, expected in test_cases:
            result = crawler._analyze_post_changes(post_data, processed_posts)
            if result == expected:
                logger.info(f"✅ Change detection: {expected}")
            else:
                logger.error(f"❌ Change detection failed: Expected {expected}, got {result}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Change detection test failed: {e}")
        return False

def test_new_post_filtering():
    """Test new post filtering with threshold"""
    logger.info("Testing new post filtering...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(min_comments_for_new_posts=5)
        crawler = ForumCrawler(config)
        
        test_cases = [
            ({"current_comment_count": 3}, False, "3 comments < 5 threshold"),
            ({"current_comment_count": 5}, True, "5 comments = 5 threshold"),
            ({"current_comment_count": 10}, True, "10 comments > 5 threshold"),
        ]
        
        for post_data, expected, description in test_cases:
            result = crawler._should_process_new_post(post_data)
            if result == expected:
                logger.info(f"✅ {description}: {result}")
            else:
                logger.error(f"❌ {description}: Expected {expected}, got {result}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"New post filtering test failed: {e}")
        return False

def test_state_recording():
    """Test recording new posts in state"""
    logger.info("Testing state recording...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test post data
        test_post = {
            "post_url": "https://lowendtalk.com/discussion/test/recording",
            "post_title": "Test Post for Recording",
            "current_comment_count": 15,
            "datetime_attr": datetime.now().isoformat()
        }
        
        # Record the post
        crawler._record_new_post_in_state(test_post)
        
        # Verify it was recorded
        time.sleep(1)
        current_state = crawler.state_manager.load_state()
        processed_posts = current_state.get("processed_posts", {})
        
        if test_post["post_url"] in processed_posts:
            recorded_data = processed_posts[test_post["post_url"]]
            recorded_count = recorded_data.get("last_comment_count", 0)
            recorded_status = recorded_data.get("status", "unknown")
            
            if recorded_count == test_post["current_comment_count"] and recorded_status == "recorded":
                logger.info(f"✅ State recording successful: {recorded_count} comments, status: {recorded_status}")
                return True
            else:
                logger.error(f"❌ State recording failed: count={recorded_count}, status={recorded_status}")
                return False
        else:
            logger.error("❌ State recording failed: Post not found in state")
            return False
            
    except Exception as e:
        logger.error(f"State recording test failed: {e}")
        return False

def test_incremental_processing_logic():
    """Test incremental processing logic"""
    logger.info("Testing incremental processing logic...")
    
    # Mock comment data
    mock_comments = [
        {"text": "This is comment 1", "id": "comment_1"},
        {"text": "This is comment 2", "id": "comment_2"},
        {"text": "This is comment 3", "id": "comment_3"},  # Last known
        {"text": "This is comment 4", "id": "comment_4"},  # New
        {"text": "This is comment 5", "id": "comment_5"},  # New
    ]
    
    # Simulate incremental processing
    last_known_content = "This is comment 3"
    prev_processed_ids = {"comment_1", "comment_2", "comment_3"}
    
    # Process from newest to oldest
    comments_to_check = list(reversed(mock_comments))
    new_comments_found = []
    
    for comment in comments_to_check:
        comment_text = comment["text"]
        comment_id = comment["id"]
        
        # Stop when reaching known content
        if comment_text[:200] == last_known_content:
            logger.info("Found last known comment, stopping")
            break
        
        # Skip if already processed
        if comment_id in prev_processed_ids:
            continue
        
        # This is a new comment
        new_comments_found.append(comment_id)
    
    expected_new_comments = ["comment_5", "comment_4"]
    
    if new_comments_found == expected_new_comments:
        logger.info(f"✅ Incremental processing: Found {len(new_comments_found)} new comments")
        return True
    else:
        logger.error(f"❌ Incremental processing failed: Expected {expected_new_comments}, got {new_comments_found}")
        return False

def test_monitoring_system_startup():
    """Test if the monitoring system can start properly"""
    logger.info("Testing monitoring system startup...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(
            monitor_interval=10,  # Short interval for testing
            num_workers=1,
            headless=True
        )
        
        crawler = ForumCrawler(config)
        
        # Test initialization
        if hasattr(crawler, 'monitoring_active') and hasattr(crawler, 'change_detection_queue'):
            logger.info("✅ Monitoring components initialized")
        else:
            logger.error("❌ Monitoring components missing")
            return False
        
        # Test configuration
        if crawler.monitor_interval == 10:
            logger.info(f"✅ Monitor interval configured: {crawler.monitor_interval}s")
        else:
            logger.error(f"❌ Monitor interval incorrect: {crawler.monitor_interval}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Monitoring system startup test failed: {e}")
        return False

def check_current_state_file():
    """Check the current state file structure"""
    logger.info("Checking current state file...")
    
    try:
        state_file = "lowendtalk_crawl_state.json"
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            processed_posts = state.get("processed_posts", {})
            logger.info(f"Current state has {len(processed_posts)} processed posts")
            
            # Check structure of a few posts
            for i, (url, data) in enumerate(list(processed_posts.items())[:3]):
                required_fields = ["last_comment_count", "last_comment_datetime", "processed_comment_ids"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    logger.warning(f"Post {i+1} missing fields: {missing_fields}")
                else:
                    logger.info(f"✅ Post {i+1} has all required fields")
            
            return True
        else:
            logger.warning("State file does not exist")
            return False
            
    except Exception as e:
        logger.error(f"State file check failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Real-Time Forum Crawler Refactoring")
    logger.info("=" * 60)
    
    # Test 1: Configuration loading
    logger.info("Test 1: Configuration loading")
    config_test = test_configuration_loading()
    
    # Test 2: Change detection logic
    logger.info("\nTest 2: Change detection logic")
    change_detection_test = test_change_detection_logic()
    
    # Test 3: New post filtering
    logger.info("\nTest 3: New post filtering")
    filtering_test = test_new_post_filtering()
    
    # Test 4: State recording
    logger.info("\nTest 4: State recording")
    recording_test = test_state_recording()
    
    # Test 5: Incremental processing logic
    logger.info("\nTest 5: Incremental processing logic")
    incremental_test = test_incremental_processing_logic()
    
    # Test 6: Monitoring system startup
    logger.info("\nTest 6: Monitoring system startup")
    startup_test = test_monitoring_system_startup()
    
    # Test 7: Current state file check
    logger.info("\nTest 7: Current state file check")
    state_check = check_current_state_file()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 40)
    logger.info(f"Configuration loading: {'PASS' if config_test else 'FAIL'}")
    logger.info(f"Change detection: {'PASS' if change_detection_test else 'FAIL'}")
    logger.info(f"New post filtering: {'PASS' if filtering_test else 'FAIL'}")
    logger.info(f"State recording: {'PASS' if recording_test else 'FAIL'}")
    logger.info(f"Incremental processing: {'PASS' if incremental_test else 'FAIL'}")
    logger.info(f"Monitoring startup: {'PASS' if startup_test else 'FAIL'}")
    logger.info(f"State file check: {'PASS' if state_check else 'FAIL'}")
    
    total_passed = sum([config_test, change_detection_test, filtering_test, 
                       recording_test, incremental_test, startup_test, state_check])
    
    if total_passed == 7:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("Real-time crawler refactoring is working correctly:")
        logger.info("  ✅ Real-time monitoring with configurable intervals")
        logger.info("  ✅ Efficient change detection and filtering")
        logger.info("  ✅ Incremental comment processing")
        logger.info("  ✅ Thread-safe state management")
        logger.info("  ✅ New post detection and recording")
        logger.info("\nReady to run: python improved_forum_crawler.py")
    elif total_passed > 0:
        logger.info(f"\n⚠️ {total_passed}/7 tests passed.")
        logger.info("Some functionality is working, but issues remain.")
    else:
        logger.error("\n💥 ALL TESTS FAILED!")
        logger.error("Refactoring needs further debugging.")
    
    return total_passed == 7

if __name__ == "__main__":
    main()
