"""
Cloudflare Bypass Forum Crawler
Specialized version to bypass Cloudflare protection
"""

import time
import logging
import json
import threading
import queue
import random
from datetime import datetime, timezone
from DrissionPage import ChromiumPage, Chromium, ChromiumOptions
from DrissionPage.common import Settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables
task_queue = queue.Queue()
results = []
results_lock = threading.Lock()
monitor_ready = threading.Event()
shutdown_event = threading.Event()

# Latest user agents for 2025
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
]

def create_stealth_browser():
    """Create a browser with maximum stealth settings"""
    try:
        # Configure DrissionPage
        Settings.set_singleton_tab_obj(False)
        
        # Create ChromiumOptions with stealth settings
        options = ChromiumOptions()
        
        # Basic stealth arguments
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-features=TranslateUI')
        options.add_argument('--disable-ipc-flooding-protection')
        
        # Random user agent
        user_agent = random.choice(USER_AGENTS)
        options.add_argument(f'--user-agent={user_agent}')
        
        # Experimental options for stealth
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Additional stealth settings
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,  # Block notifications
                "images": 2,  # Block images for faster loading
            },
            "profile.managed_default_content_settings": {
                "images": 2
            }
        }
        options.add_experimental_option("prefs", prefs)
        
        # Create browser
        browser = Chromium(addr_or_opts=options)
        
        logger.info(f"Stealth browser created with UA: {user_agent[:60]}...")
        return browser
        
    except Exception as e:
        logger.error(f"Failed to create stealth browser: {e}")
        # Fallback to basic browser
        try:
            browser = Chromium()
            logger.info("Created basic browser as fallback")
            return browser
        except Exception as fallback_error:
            logger.error(f"Fallback browser creation failed: {fallback_error}")
            raise

def safe_log(text, max_length=80):
    """Safe logging to avoid Unicode errors"""
    try:
        return text[:max_length]
    except:
        return text.encode('ascii', 'ignore').decode('ascii')[:max_length]

def wait_for_cloudflare(page, max_wait=30):
    """Wait for Cloudflare challenge to complete"""
    logger.info("Checking for Cloudflare challenge...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            # Check if we're on a Cloudflare challenge page
            title = page.title.lower() if hasattr(page, 'title') else ""
            
            if 'cloudflare' in title or 'checking your browser' in title:
                logger.info("Cloudflare challenge detected, waiting...")
                time.sleep(2)
                continue
            
            # Check if page has loaded properly
            if 'lowendtalk' in title:
                logger.info("✅ Successfully bypassed Cloudflare")
                return True
                
            # Wait a bit more
            time.sleep(1)
            
        except Exception as e:
            logger.debug(f"Error checking Cloudflare status: {e}")
            time.sleep(1)
    
    logger.warning("⚠️ Cloudflare bypass timeout")
    return False

def extract_post_data(item):
    """Extract post data with error handling"""
    try:
        # Find title link
        title_link = item.ele('a', timeout=3)
        if not title_link:
            return None
        
        title = title_link.text.strip()
        url = title_link.link
        
        if not title or not url:
            return None
        
        # Ensure absolute URL
        if not url.startswith('http'):
            url = 'https://lowendtalk.com' + url
        
        # Try to extract comment count
        comment_count = 0
        try:
            # Look for comment count elements
            count_elements = item.eles('.Number')
            for elem in count_elements:
                text = elem.text.strip()
                if text.isdigit():
                    comment_count = int(text)
                    break
                elif 'k' in text.lower():
                    try:
                        num = float(text.lower().replace('k', ''))
                        comment_count = int(num * 1000)
                        break
                    except:
                        pass
        except Exception as e:
            logger.debug(f"Could not extract comment count: {e}")
        
        logger.info(f"Extracted: {safe_log(title)} (Comments: {comment_count})")
        
        return {
            "post_url": url,
            "post_title": title,
            "current_comment_count": comment_count,
            "datetime_attr": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error extracting post data: {e}")
        return None

def monitor_thread():
    """Monitor thread with Cloudflare bypass"""
    logger.info("Starting Cloudflare bypass monitor thread...")
    
    browser = None
    try:
        browser = create_stealth_browser()
        page = browser.new_tab()
        
        # Add random delays to seem more human
        time.sleep(random.uniform(2, 5))
        
        while not shutdown_event.is_set():
            try:
                # Navigate to forum with retry logic
                url = "https://lowendtalk.com/categories/offers"
                logger.info(f"Loading: {url}")
                
                page.get(url)
                
                # Wait for Cloudflare challenge
                if not wait_for_cloudflare(page, max_wait=30):
                    logger.error("Failed to bypass Cloudflare, retrying...")
                    time.sleep(60)  # Wait before retry
                    continue
                
                # Random delay to seem human
                time.sleep(random.uniform(3, 7))
                
                # Find posts container
                container = page.ele('tag:ul@class=DataList Discussions', timeout=15)
                if not container:
                    logger.error("Could not find posts container")
                    time.sleep(30)
                    continue
                
                # Get posts
                posts = container.eles('xpath:./li[contains(@class, "Item")]')
                logger.info(f"Found {len(posts)} posts")
                
                # Process first 3 posts to avoid being too aggressive
                posts_to_process = posts[:3]
                new_tasks = 0
                
                for i, post in enumerate(posts_to_process):
                    try:
                        logger.info(f"Processing post {i+1}/{len(posts_to_process)}")
                        
                        post_data = extract_post_data(post)
                        if post_data:
                            task_queue.put(post_data)
                            new_tasks += 1
                            logger.info(f"Queued: {safe_log(post_data['post_title'])}")
                        
                        # Random delay between posts
                        time.sleep(random.uniform(1, 3))
                        
                    except Exception as e:
                        logger.error(f"Error processing post {i+1}: {e}")
                        continue
                
                logger.info(f"Monitor cycle completed. Queued {new_tasks} new tasks.")
                
                # Signal that monitor is ready (first time only)
                if not monitor_ready.is_set():
                    monitor_ready.set()
                    logger.info("Monitor ready - workers can start")
                
                # Wait before next cycle (longer to avoid detection)
                wait_time = random.uniform(90, 150)  # 1.5-2.5 minutes
                logger.info(f"Waiting {wait_time:.1f} seconds before next cycle")
                time.sleep(wait_time)
                
            except Exception as e:
                logger.error(f"Error in monitor cycle: {e}")
                time.sleep(60)  # Wait before retry
        
    except Exception as e:
        logger.error(f"Monitor thread error: {e}")
    finally:
        monitor_ready.set()  # Always set the event
        if browser:
            try:
                browser.quit()
            except:
                pass
        logger.info("Monitor thread ended")

def worker_thread(worker_id):
    """Worker thread with Cloudflare bypass"""
    logger.info(f"Worker {worker_id} starting...")
    
    # Wait for monitor to be ready
    if not monitor_ready.wait(timeout=180):  # Longer timeout for Cloudflare
        logger.warning(f"Worker {worker_id}: Monitor not ready, exiting")
        return
    
    browser = None
    try:
        browser = create_stealth_browser()
        page = browser.new_tab()
        
        while not shutdown_event.is_set():
            try:
                # Get task from queue
                task_data = task_queue.get(timeout=60)
                
                post_url = task_data["post_url"]
                post_title = task_data["post_title"]
                
                logger.info(f"Worker {worker_id}: Processing {safe_log(post_title)}")
                
                # Navigate to post with random delay
                time.sleep(random.uniform(2, 5))
                page.get(post_url)
                
                # Wait for Cloudflare if needed
                wait_for_cloudflare(page, max_wait=20)
                
                # Random delay
                time.sleep(random.uniform(3, 6))
                
                # Scroll to load comments
                page.scroll.to_bottom()
                time.sleep(random.uniform(2, 4))
                
                # Get comments
                comments = page.eles('.Comment')
                logger.info(f"Worker {worker_id}: Found {len(comments)} comments")
                
                # Check for flash sales
                flash_sales_found = []
                keywords = ['sale', 'offer', 'discount', 'promo', '$', 'gb', 'vps', 'hosting', 'deal']
                
                for j, comment in enumerate(comments[:15]):  # Limit to avoid being too aggressive
                    try:
                        comment_text = comment.text.strip()
                        if len(comment_text) > 30:
                            text_lower = comment_text.lower()
                            keyword_count = sum(1 for keyword in keywords if keyword in text_lower)
                            
                            if keyword_count >= 3:
                                flash_sale = {
                                    "post_title": post_title,
                                    "post_url": post_url,
                                    "comment_id": f"comment_{j}",
                                    "comment_text": comment_text[:300] + "..." if len(comment_text) > 300 else comment_text,
                                    "keywords_found": keyword_count,
                                    "crawled_time": datetime.now(timezone.utc).isoformat(),
                                    "worker_id": worker_id
                                }
                                flash_sales_found.append(flash_sale)
                                logger.info(f"Worker {worker_id}: 🎉 Flash sale detected! Keywords: {keyword_count}")
                    except Exception as e:
                        logger.debug(f"Error processing comment {j}: {e}")
                
                # Save results
                if flash_sales_found:
                    with results_lock:
                        results.extend(flash_sales_found)
                        with open('cloudflare_bypass_results.json', 'w', encoding='utf-8') as f:
                            json.dump(results, f, ensure_ascii=False, indent=2)
                        logger.info(f"Worker {worker_id}: Saved {len(flash_sales_found)} flash sales")
                
                task_queue.task_done()
                
                # Random delay before next task
                time.sleep(random.uniform(5, 10))
                
            except queue.Empty:
                logger.debug(f"Worker {worker_id}: No tasks available")
                continue
            except Exception as e:
                logger.error(f"Worker {worker_id}: Error processing task: {e}")
                try:
                    task_queue.task_done()
                except:
                    pass
                time.sleep(10)  # Wait before retry
                
    except Exception as e:
        logger.error(f"Worker {worker_id}: Critical error: {e}")
    finally:
        if browser:
            try:
                browser.quit()
            except:
                pass
        logger.info(f"Worker {worker_id}: Ended")

def main():
    """Main function"""
    logger.info("🚀 Starting Cloudflare Bypass Forum Crawler...")
    
    # Start monitor thread
    monitor = threading.Thread(target=monitor_thread, name="Monitor", daemon=True)
    monitor.start()
    
    # Start 1 worker thread (less aggressive)
    worker = threading.Thread(target=worker_thread, args=(1,), name="Worker-1", daemon=True)
    worker.start()
    
    # Main loop
    try:
        while True:
            time.sleep(30)
            logger.info(f"📊 Status - Queue: {task_queue.qsize()}, Results: {len(results)}")
            
    except KeyboardInterrupt:
        logger.info("🛑 Shutting down...")
        shutdown_event.set()
        
        monitor.join(timeout=15)
        worker.join(timeout=15)
        
        logger.info(f"✅ Crawler stopped. Total flash sales found: {len(results)}")

if __name__ == "__main__":
    main()
