"""
Enhanced Forum Crawler Launcher with Cloudflare Bypass
This script provides a robust way to start the forum crawler with proper Cloudflare handling
"""

import sys
import time
import logging
import subprocess
from pathlib import Path

def setup_logging():
    """Setup logging for the launcher"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('crawler_launcher.log')
        ]
    )
    return logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are installed"""
    logger = logging.getLogger(__name__)
    
    try:
        import DrissionPage
        logger.info("✅ DrissionPage is installed")
    except ImportError:
        logger.error("❌ DrissionPage is not installed. Please install it with: pip install DrissionPage")
        return False
    
    # Check if Chrome/Chromium is available
    try:
        from DrissionPage import Chromium
        browser = Chromium()
        browser.quit()
        logger.info("✅ Chrome/Chromium is available")
    except Exception as e:
        logger.error(f"❌ Chrome/Chromium is not available: {e}")
        logger.error("Please install Chrome or Chromium browser")
        return False
    
    return True

def test_cloudflare_bypass():
    """Test if Cloudflare bypass is working"""
    logger = logging.getLogger(__name__)
    
    logger.info("🧪 Testing Cloudflare bypass...")
    
    try:
        # Run the test script
        result = subprocess.run([
            sys.executable, 'test_cloudflare_bypass.py'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            logger.info("✅ Cloudflare bypass test passed")
            return True
        else:
            logger.error("❌ Cloudflare bypass test failed")
            logger.error(f"Error output: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Cloudflare bypass test timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Error running Cloudflare bypass test: {e}")
        return False

def create_default_config():
    """Create default configuration if it doesn't exist"""
    logger = logging.getLogger(__name__)
    
    config_file = Path('crawler_config.json')
    if not config_file.exists():
        logger.info("📝 Creating default configuration file...")
        
        default_config = {
            "base_url": "https://lowendtalk.com/",
            "monitor_url": "https://lowendtalk.com",
            "state_file": "lowendtalk_crawl_state.json",
            "results_file": "lowendtalk_flash_sales.json",
            "num_workers": 3,
            "monitor_timeout": 90,
            "worker_timeout": 30,
            "queue_timeout": 30,
            "headless": False,
            "refresh_interval": 60,
            "scroll_delay": 2.0,
            "page_load_delay": 5.0,
            "min_request_delay": 2.0,
            "max_request_delay": 5.0,
            "max_posts_to_check": 10,
            "max_retries": 3
        }
        
        import json
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        logger.info("✅ Default configuration created")

def run_crawler():
    """Run the main crawler"""
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting Forum Crawler with Cloudflare bypass...")
    
    try:
        # Run the improved crawler
        subprocess.run([sys.executable, 'improved_forum_crawler.py'], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Crawler failed with exit code {e.returncode}")
        return False
    except KeyboardInterrupt:
        logger.info("⏹️ Crawler stopped by user")
        return True
    except Exception as e:
        logger.error(f"❌ Error running crawler: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    logger = setup_logging()
    
    logger.info("🎯 Forum Crawler Launcher with Cloudflare Bypass")
    logger.info("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        logger.error("💥 Dependency check failed. Please fix the issues above.")
        return False
    
    # Create default config if needed
    create_default_config()
    
    # Test Cloudflare bypass
    if not test_cloudflare_bypass():
        logger.warning("⚠️ Cloudflare bypass test failed, but continuing anyway...")
        logger.warning("The crawler may encounter issues with Cloudflare verification.")
        
        response = input("Do you want to continue anyway? (y/N): ")
        if response.lower() != 'y':
            logger.info("Exiting...")
            return False
    
    # Run the crawler
    logger.info("🏃 Starting the main crawler...")
    success = run_crawler()
    
    if success:
        logger.info("✅ Crawler completed successfully")
    else:
        logger.error("❌ Crawler encountered errors")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
