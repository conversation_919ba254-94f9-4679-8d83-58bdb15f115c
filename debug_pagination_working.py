"""
Working pagination debugging script with correct DrissionPage API
"""

import time
import logging
import re
from DrissionPage import ChromiumPage

# Setup detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_page():
    """Create a ChromiumPage for testing"""
    try:
        # Use ChromiumPage directly with options
        page = ChromiumPage()
        logger.info("✅ Test page created")
        return page
        
    except Exception as e:
        logger.error(f"❌ Failed to create test page: {e}")
        return None

def wait_for_cloudflare_bypass(page, max_wait=30):
    """Wait for Cloudflare verification to complete"""
    logger.info("🔍 Checking for Cloudflare verification...")
    
    cloudflare_indicators = [
        'checking your browser',
        'ddos protection',
        'please wait',
        'cloudflare',
        'just a moment',
        'verifying you are human'
    ]
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            current_title = page.title.lower() if hasattr(page, 'title') else ""
            current_url = page.url if hasattr(page, 'url') else ""
            
            is_cloudflare = any(indicator in current_title for indicator in cloudflare_indicators)
            
            if not is_cloudflare and 'lowendtalk.com' in current_url:
                logger.info("✅ Successfully bypassed Cloudflare verification!")
                return True
            
            if is_cloudflare:
                elapsed = int(time.time() - start_time)
                logger.info(f"⏳ Cloudflare verification in progress... ({elapsed}s)")
            
            time.sleep(2)
            
        except Exception as e:
            logger.warning(f"Error checking page status: {e}")
            time.sleep(2)
    
    logger.error(f"❌ Cloudflare verification timed out after {max_wait} seconds")
    return False

def analyze_page_html(page):
    """Analyze the raw HTML to understand pagination structure"""
    logger.info("🔍 Analyzing page HTML structure...")
    
    try:
        # Get page HTML
        html = page.html
        
        # Look for pagination patterns in HTML
        pagination_patterns = [
            r'href="[^"]*\/p\d+[^"]*"',  # /p2, /p3, etc.
            r'href="[^"]*page=\d+[^"]*"',  # page=2, page=3, etc.
            r'class="[^"]*[Pp]ager[^"]*"',  # Pager classes
            r'class="[^"]*[Nn]avigation[^"]*"',  # Navigation classes
        ]
        
        found_patterns = {}
        
        for pattern in pagination_patterns:
            matches = re.findall(pattern, html)
            if matches:
                found_patterns[pattern] = matches[:5]  # Show first 5 matches
        
        if found_patterns:
            logger.info("✅ Found pagination patterns in HTML:")
            for pattern, matches in found_patterns.items():
                logger.info(f"  📄 Pattern {pattern}:")
                for match in matches:
                    logger.info(f"    - {match}")
        else:
            logger.warning("⚠️ No pagination patterns found in HTML")
        
        return found_patterns
        
    except Exception as e:
        logger.error(f"❌ Error analyzing HTML: {e}")
        return {}

def analyze_pagination_elements(page):
    """Analyze all potential pagination elements on the page"""
    logger.info("🔍 Analyzing pagination elements...")
    
    # Check all possible selectors
    selectors_to_test = [
        'a[href*="/p"]',
        '.Pager a',
        '.PageNavigation a', 
        'a[href*="page="]',
        'a[href*="/p2"]',
        'a[href*="/p3"]',
        'a[href*="/p4"]',
        'a[href*="/p5"]',
        'a',  # All links
    ]
    
    found_elements = {}
    
    for selector in selectors_to_test:
        try:
            elements = page.eles(selector)
            if elements:
                found_elements[selector] = []
                
                # For 'a' selector, filter for pagination-like links
                if selector == 'a':
                    pagination_links = []
                    for elem in elements:
                        try:
                            href = elem.attr('href') or ''
                            text = elem.text.strip()
                            
                            # Check if it looks like pagination
                            if (('/p' in href and re.search(r'/p\d+', href)) or
                                ('page=' in href) or
                                (text.isdigit() and int(text) > 1) or
                                text in ['Next', '»', 'Last', '>', 'Previous', '‹', '<']):
                                pagination_links.append(elem)
                        except:
                            continue
                    
                    elements = pagination_links[:10]  # Limit to 10
                
                for elem in elements[:5]:  # Show first 5
                    try:
                        text = elem.text.strip()
                        href = elem.attr('href') or ''
                        found_elements[selector].append({
                            'text': text,
                            'href': href
                        })
                    except:
                        continue
        except Exception as e:
            logger.debug(f"Selector {selector} failed: {e}")
    
    # Report findings
    if found_elements:
        logger.info("✅ Found pagination elements:")
        for selector, elements in found_elements.items():
            if elements:  # Only show selectors that found elements
                logger.info(f"  📄 {selector}: {len(elements)} elements")
                for elem in elements:
                    logger.info(f"    - '{elem['text']}' -> {elem['href']}")
    else:
        logger.warning("⚠️ No pagination elements found with any selector")
    
    return found_elements

def test_pagination_navigation(page, found_elements):
    """Test actual navigation using found pagination elements"""
    logger.info("🧪 Testing pagination navigation...")
    
    original_url = page.url
    logger.info(f"📍 Original URL: {original_url}")
    
    # Try to find the highest page number
    max_page = 1
    best_link = None
    
    for selector, elements in found_elements.items():
        if not elements:
            continue
            
        for elem_info in elements:
            try:
                text = elem_info['text']
                href = elem_info['href']
                
                # Try to extract page number
                page_num = None
                
                # From text
                if text.isdigit():
                    page_num = int(text)
                
                # From href
                if not page_num:
                    match = re.search(r'/p(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                
                if not page_num:
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                
                if page_num and page_num > max_page:
                    max_page = page_num
                    best_link = (selector, text, href)
                    logger.info(f"📈 Found higher page number: {page_num}")
                    
            except Exception as e:
                logger.debug(f"Error analyzing element: {e}")
                continue
    
    # Try to navigate to the highest page found
    if best_link and max_page > 1:
        selector, text, href = best_link
        logger.info(f"🚀 Attempting to navigate to page {max_page}")
        logger.info(f"🔗 Using link: '{text}' -> {href}")
        
        try:
            # Find the actual element again
            elements = page.eles(selector)
            target_element = None
            
            for elem in elements:
                if elem.text.strip() == text and elem.attr('href') == href:
                    target_element = elem
                    break
            
            if target_element:
                # Click the element
                target_element.click()
                time.sleep(4)  # Wait longer for navigation
                
                new_url = page.url
                logger.info(f"📍 New URL: {new_url}")
                
                if new_url != original_url:
                    logger.info("✅ Navigation successful!")
                    
                    # Verify we're on the expected page
                    if f"/p{max_page}" in new_url or f"page={max_page}" in new_url:
                        logger.info(f"✅ Confirmed on page {max_page}")
                    else:
                        logger.warning("⚠️ URL doesn't contain expected page number")
                    
                    # Count comments on new page
                    comments = page.eles('.Comment')
                    logger.info(f"💬 Comments on new page: {len(comments)}")
                    
                    return True
                else:
                    logger.warning("⚠️ URL didn't change after clicking")
            else:
                logger.error("❌ Could not find target element to click")
                
        except Exception as e:
            logger.error(f"❌ Error during navigation: {e}")
    
    # Try alternative navigation methods
    logger.info("🔄 Trying alternative navigation methods...")
    
    alternative_selectors = [
        ('Next link', 'a:contains("Next")'),
        ('Right arrow', 'a:contains("»")'),
        ('Last link', 'a:contains("Last")'),
    ]
    
    for name, selector in alternative_selectors:
        try:
            elements = page.eles(selector)
            if elements:
                elem = elements[0]
                logger.info(f"🔗 Trying {name}: '{elem.text}' -> {elem.attr('href')}")
                
                elem.click()
                time.sleep(3)
                
                new_url = page.url
                if new_url != original_url:
                    logger.info(f"✅ {name} navigation successful!")
                    return True
                else:
                    logger.warning(f"⚠️ {name} didn't change URL")
                    
        except Exception as e:
            logger.debug(f"{name} failed: {e}")
            continue
    
    logger.error("❌ No successful navigation achieved")
    return False

def test_specific_post(post_url):
    """Test pagination on a specific post"""
    page = None
    
    try:
        page = create_test_page()
        if not page:
            return False
        
        logger.info(f"🌐 Testing pagination on: {post_url}")
        page.get(post_url)
        
        if not wait_for_cloudflare_bypass(page):
            logger.error("❌ Failed to bypass Cloudflare")
            return False
        
        # Count initial comments
        initial_comments = page.eles('.Comment')
        logger.info(f"📝 Initial comments: {len(initial_comments)}")
        
        # Analyze HTML structure
        html_patterns = analyze_page_html(page)
        
        # Analyze pagination elements
        found_elements = analyze_pagination_elements(page)
        
        if not found_elements:
            logger.warning("⚠️ No pagination elements found - post may have only one page")
            return False
        
        # Test navigation
        navigation_success = test_pagination_navigation(page, found_elements)
        
        return navigation_success
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if page:
            try:
                page.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def main():
    """Main test function"""
    logger.info("🚀 Working Pagination Debug")
    logger.info("=" * 40)
    
    # Test with known URLs that should have pagination
    test_urls = [
        "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price",
        "https://lowendtalk.com/categories/offers",  # Try offers page first
    ]
    
    for i, url in enumerate(test_urls, 1):
        logger.info(f"\n🧪 Test {i}: {url}")
        
        success = test_specific_post(url)
        
        if success:
            logger.info(f"✅ Test {i} PASSED")
            return True
        else:
            logger.error(f"❌ Test {i} FAILED")
        
        if i < len(test_urls):
            time.sleep(2)
    
    logger.error("💥 All tests failed!")
    return False

if __name__ == "__main__":
    main()
