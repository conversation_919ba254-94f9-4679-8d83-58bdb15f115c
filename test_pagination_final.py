"""
Final test of the pagination functionality using the correct class structure
"""

import time
import logging
from improved_forum_crawler import CrawlerConfig, ForumCrawler, BrowserManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pagination_with_crawler():
    """Test pagination using the ForumCrawler class"""
    crawler = None
    
    try:
        # Create config with pagination enabled
        config = CrawlerConfig(
            enable_pagination=True,
            max_pages_to_check=3,
            comments_per_page=25,
            headless=False,  # For debugging
            refresh_interval=1,
            num_workers=1  # Just one worker for testing
        )
        
        logger.info("📋 Config created:")
        logger.info(f"   enable_pagination: {config.enable_pagination}")
        logger.info(f"   max_pages_to_check: {config.max_pages_to_check}")
        logger.info(f"   comments_per_page: {config.comments_per_page}")
        
        # Create crawler
        crawler = ForumCrawler(config)
        
        # Start browser
        crawler.browser_manager = BrowserManager(config)
        crawler.browser_manager.start_browser()
        
        # Create test page
        test_page = crawler.browser_manager.create_tab()
        if not test_page:
            logger.error("❌ Failed to create test page")
            return False
        
        # Test URL
        test_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        logger.info(f"🌐 Testing with: {test_url}")
        
        # Navigate to test page
        test_page.get(test_url)
        time.sleep(3)
        
        # Handle Cloudflare
        if not crawler.browser_manager.handle_cloudflare_verification(test_page, max_wait_time=30):
            logger.warning("⚠️ Cloudflare verification failed, continuing anyway...")
        
        # Count initial comments
        initial_comments = test_page.eles('.Comment')
        logger.info(f"💬 Initial comments: {len(initial_comments)}")
        
        # Test pagination navigation
        worker_id = "TEST-WORKER"
        expected_comment_count = len(initial_comments) + 5  # Simulate increase
        
        logger.info("🔍 Testing pagination navigation...")
        last_page_reached = crawler._navigate_to_last_page(test_page, worker_id, expected_comment_count)
        
        if last_page_reached:
            logger.info("✅ Pagination navigation successful!")
            
            # Check new URL and comments
            new_url = test_page.url
            new_comments = test_page.eles('.Comment')
            logger.info(f"📍 New URL: {new_url}")
            logger.info(f"💬 Comments after navigation: {len(new_comments)}")
            
        else:
            logger.info("ℹ️ No pagination found (normal for single-page posts)")
        
        # Test comment collection
        logger.info("📝 Testing comment collection...")
        all_comments = crawler._get_all_comments_with_pagination(test_page, worker_id, expected_comment_count)
        
        if all_comments:
            logger.info(f"✅ Successfully collected {len(all_comments)} comments")
            
            # Show sample comments
            for i, comment in enumerate(all_comments[:3]):
                try:
                    comment_id = comment.attr('id') or f"comment-{i}"
                    comment_text = comment.text.strip()[:50] + "..." if len(comment.text.strip()) > 50 else comment.text.strip()
                    logger.info(f"   💬 {comment_id}: {comment_text}")
                except:
                    logger.info(f"   💬 Comment {i+1}: [Could not get details]")
            
            return True
        else:
            logger.error("❌ No comments collected")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if crawler and hasattr(crawler, 'browser_manager'):
            try:
                crawler.browser_manager.cleanup()
                logger.info("🔒 Browser closed")
            except:
                pass

def test_multiple_posts():
    """Test pagination with multiple different posts"""
    test_urls = [
        "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price",
        "https://lowendtalk.com/discussion/137719/lowendtalk-community-rules",
    ]
    
    success_count = 0
    
    for i, test_url in enumerate(test_urls, 1):
        logger.info(f"\n🧪 Test {i}/{len(test_urls)}: {test_url}")
        
        crawler = None
        try:
            # Create fresh crawler for each test
            config = CrawlerConfig(
                enable_pagination=True,
                max_pages_to_check=2,
                comments_per_page=25,
                headless=False
            )
            
            crawler = ForumCrawler(config)
            crawler.browser_manager = BrowserManager(config)
            crawler.browser_manager.start_browser()
            
            test_page = crawler.browser_manager.create_tab()
            test_page.get(test_url)
            time.sleep(3)
            
            # Handle Cloudflare
            crawler.browser_manager.handle_cloudflare_verification(test_page, max_wait_time=20)
            
            # Count comments
            comments = test_page.eles('.Comment')
            logger.info(f"💬 Found {len(comments)} comments")
            
            # Test pagination
            worker_id = f"TEST-{i}"
            all_comments = crawler._get_all_comments_with_pagination(test_page, worker_id, len(comments) + 5)
            
            if all_comments:
                logger.info(f"✅ Test {i} successful: {len(all_comments)} comments collected")
                success_count += 1
            else:
                logger.warning(f"⚠️ Test {i} failed: No comments collected")
            
        except Exception as e:
            logger.error(f"❌ Test {i} failed: {e}")
        finally:
            if crawler and hasattr(crawler, 'browser_manager'):
                try:
                    crawler.browser_manager.cleanup()
                except:
                    pass
    
    logger.info(f"\n📊 Multiple post test results: {success_count}/{len(test_urls)} successful")
    return success_count > 0

def test_worker_simulation():
    """Simulate the actual worker process that would use pagination"""
    logger.info("\n🧪 Testing worker simulation...")
    
    crawler = None
    try:
        # Load actual config
        config = CrawlerConfig.from_file("crawler_config.json")
        logger.info(f"📋 Loaded config: pagination={config.enable_pagination}")
        
        crawler = ForumCrawler(config)
        crawler.browser_manager = BrowserManager(config)
        crawler.browser_manager.start_browser()
        
        # Simulate worker processing a post
        test_page = crawler.browser_manager.create_tab()
        post_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        
        logger.info(f"🔄 Simulating worker processing: {post_url}")
        
        # This simulates the worker's _process_post_comments method
        test_page.get(post_url)
        time.sleep(3)
        
        # Handle Cloudflare
        crawler.browser_manager.handle_cloudflare_verification(test_page, max_wait_time=20)
        
        # Get current comment count (simulating what monitor detected)
        current_comments = test_page.eles('.Comment')
        current_comment_count = len(current_comments)
        
        logger.info(f"💬 Current comments: {current_comment_count}")
        
        # Simulate detecting an increase (this is what triggers pagination)
        simulated_increase = current_comment_count + 3
        logger.info(f"📈 Simulating comment increase to: {simulated_increase}")
        
        # This is the key part - get all comments with pagination
        worker_id = "WORKER-SIM"
        all_comments = crawler._get_all_comments_with_pagination(test_page, worker_id, simulated_increase)
        
        if all_comments:
            logger.info(f"✅ Worker simulation successful: {len(all_comments)} comments processed")
            return True
        else:
            logger.error("❌ Worker simulation failed: No comments processed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Worker simulation failed: {e}")
        return False
    finally:
        if crawler and hasattr(crawler, 'browser_manager'):
            try:
                crawler.browser_manager.cleanup()
            except:
                pass

def main():
    """Main test function"""
    logger.info("🚀 Final Pagination Test Suite")
    logger.info("=" * 50)
    
    # Test 1: Basic pagination with crawler
    logger.info("📋 Test 1: Basic pagination functionality")
    basic_test = test_pagination_with_crawler()
    
    # Test 2: Multiple posts
    logger.info("\n📋 Test 2: Multiple posts test")
    multiple_test = test_multiple_posts()
    
    # Test 3: Worker simulation
    logger.info("\n📋 Test 3: Worker simulation")
    worker_test = test_worker_simulation()
    
    # Final summary
    logger.info("\n📊 Final Test Results:")
    logger.info(f"   Basic pagination: {'✅ PASS' if basic_test else '❌ FAIL'}")
    logger.info(f"   Multiple posts: {'✅ PASS' if multiple_test else '❌ FAIL'}")
    logger.info(f"   Worker simulation: {'✅ PASS' if worker_test else '❌ FAIL'}")
    
    total_passed = sum([basic_test, multiple_test, worker_test])
    
    if total_passed == 3:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("The pagination functionality is working correctly.")
        logger.info("The crawler should now properly handle comment increases by navigating to the newest comments.")
    elif total_passed > 0:
        logger.info(f"\n✅ {total_passed}/3 tests passed.")
        logger.info("The pagination functionality is partially working.")
    else:
        logger.error("\n💥 ALL TESTS FAILED!")
        logger.error("The pagination functionality needs further debugging.")
    
    return total_passed > 0

if __name__ == "__main__":
    main()
