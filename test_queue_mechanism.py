"""
Test the queue mechanism between monitor and workers
"""

import time
import logging
import threading
import queue
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_basic_queue():
    """Test basic queue functionality"""
    logger.info("Testing basic queue functionality...")
    
    try:
        test_queue = queue.Queue()
        
        # Add items to queue
        test_items = [
            {"id": 1, "data": "test item 1"},
            {"id": 2, "data": "test item 2"},
            {"id": 3, "data": "test item 3"}
        ]
        
        for item in test_items:
            test_queue.put(item)
            logger.info(f"Added item {item['id']} to queue")
        
        logger.info(f"Queue size: {test_queue.qsize()}")
        
        # Get items from queue
        retrieved_items = []
        while not test_queue.empty():
            item = test_queue.get()
            retrieved_items.append(item)
            logger.info(f"Retrieved item {item['id']} from queue")
            test_queue.task_done()
        
        if len(retrieved_items) == len(test_items):
            logger.info("Basic queue test PASSED")
            return True
        else:
            logger.error(f"Basic queue test FAILED: Expected {len(test_items)}, got {len(retrieved_items)}")
            return False
            
    except Exception as e:
        logger.error(f"Basic queue test failed: {e}")
        return False

def test_threaded_queue():
    """Test queue with multiple threads (simulating monitor and workers)"""
    logger.info("Testing threaded queue functionality...")
    
    try:
        test_queue = queue.Queue()
        results = []
        shutdown_event = threading.Event()
        
        def producer_thread():
            """Simulates monitor adding tasks"""
            logger.info("Producer thread started")
            for i in range(5):
                task = {
                    "id": i,
                    "post_title": f"Test Post {i}",
                    "post_url": f"https://example.com/post/{i}",
                    "current_comment_count": 10 + i
                }
                test_queue.put(task)
                logger.info(f"Producer: Added task {i} to queue (size: {test_queue.qsize()})")
                time.sleep(1)  # Simulate time between posts
            
            logger.info("Producer thread finished")
        
        def consumer_thread(worker_id):
            """Simulates worker processing tasks"""
            logger.info(f"Consumer {worker_id} started")
            
            while not shutdown_event.is_set():
                try:
                    task = test_queue.get(timeout=2)
                    logger.info(f"Consumer {worker_id}: Picked up task {task['id']} - {task['post_title']}")
                    
                    # Simulate processing time
                    time.sleep(0.5)
                    
                    results.append(f"Worker {worker_id} processed task {task['id']}")
                    test_queue.task_done()
                    
                    logger.info(f"Consumer {worker_id}: Completed task {task['id']}")
                    
                except queue.Empty:
                    logger.debug(f"Consumer {worker_id}: Queue empty, waiting...")
                    continue
                except Exception as e:
                    logger.error(f"Consumer {worker_id}: Error processing task: {e}")
                    continue
            
            logger.info(f"Consumer {worker_id} finished")
        
        # Start producer and consumers
        producer = threading.Thread(target=producer_thread)
        consumer1 = threading.Thread(target=consumer_thread, args=("Worker-1",))
        consumer2 = threading.Thread(target=consumer_thread, args=("Worker-2",))
        
        producer.start()
        consumer1.start()
        consumer2.start()
        
        # Wait for producer to finish
        producer.join()
        
        # Wait for queue to be empty
        test_queue.join()
        
        # Shutdown consumers
        shutdown_event.set()
        consumer1.join(timeout=5)
        consumer2.join(timeout=5)
        
        logger.info(f"Threaded queue test completed. Results: {len(results)} tasks processed")
        for result in results:
            logger.info(f"  {result}")
        
        if len(results) == 5:  # Should have processed all 5 tasks
            logger.info("Threaded queue test PASSED")
            return True
        else:
            logger.error(f"Threaded queue test FAILED: Expected 5 tasks, processed {len(results)}")
            return False
            
    except Exception as e:
        logger.error(f"Threaded queue test failed: {e}")
        return False

def test_crawler_queue_mechanism():
    """Test the actual crawler queue mechanism"""
    logger.info("Testing crawler queue mechanism...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(
            num_workers=2,
            monitor_timeout=30,
            queue_timeout=2
        )
        
        crawler = ForumCrawler(config)
        
        # Check if queue is initialized
        if hasattr(crawler, 'task_queue'):
            logger.info("Crawler task queue initialized")
            initial_size = crawler.task_queue.qsize()
            logger.info(f"Initial queue size: {initial_size}")
        else:
            logger.error("Crawler task queue not found!")
            return False
        
        # Add test tasks to queue
        test_tasks = [
            {
                "post_title": "Test Post 1",
                "post_url": "https://lowendtalk.com/discussion/test/1",
                "current_comment_count": 50,
                "datetime_attr": datetime.now().isoformat()
            },
            {
                "post_title": "Test Post 2", 
                "post_url": "https://lowendtalk.com/discussion/test/2",
                "current_comment_count": 75,
                "datetime_attr": datetime.now().isoformat()
            }
        ]
        
        for task in test_tasks:
            crawler.task_queue.put(task)
            queue_size = crawler.task_queue.qsize()
            logger.info(f"Added test task: {task['post_title']} (Queue size: {queue_size})")
        
        # Check queue size
        final_size = crawler.task_queue.qsize()
        logger.info(f"Final queue size: {final_size}")
        
        if final_size == len(test_tasks):
            logger.info("Crawler queue mechanism test PASSED")
            
            # Clean up queue
            while not crawler.task_queue.empty():
                crawler.task_queue.get()
                crawler.task_queue.task_done()
            
            return True
        else:
            logger.error(f"Crawler queue mechanism test FAILED: Expected {len(test_tasks)}, got {final_size}")
            return False
            
    except Exception as e:
        logger.error(f"Crawler queue mechanism test failed: {e}")
        return False

def test_worker_startup():
    """Test if workers can start properly"""
    logger.info("Testing worker startup...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler, BrowserManager
        
        config = CrawlerConfig(
            num_workers=1,
            monitor_timeout=30,
            headless=True  # Use headless for testing
        )
        
        crawler = ForumCrawler(config)
        
        # Initialize browser manager
        browser_manager = BrowserManager(config)
        browser_manager.start_browser()
        
        # Set monitor ready event (simulate monitor being ready)
        crawler.monitor_ready_event.set()
        logger.info("Monitor ready event set")
        
        # Start one worker thread
        worker_thread = threading.Thread(
            target=crawler._worker_thread_func,
            args=("TEST-WORKER", browser_manager)
        )
        
        logger.info("Starting test worker thread...")
        worker_thread.start()
        
        # Wait a bit for worker to initialize
        time.sleep(5)
        
        # Add a test task
        test_task = {
            "post_title": "Worker Test Post",
            "post_url": "https://lowendtalk.com/discussion/test/worker",
            "current_comment_count": 25,
            "datetime_attr": datetime.now().isoformat()
        }
        
        crawler.task_queue.put(test_task)
        logger.info("Added test task for worker")
        
        # Wait for worker to process
        time.sleep(10)
        
        # Check if task was processed
        queue_size = crawler.task_queue.qsize()
        logger.info(f"Queue size after processing: {queue_size}")
        
        # Shutdown
        import improved_forum_crawler
        improved_forum_crawler.shutdown_event.set()
        worker_thread.join(timeout=10)
        
        browser_manager.cleanup()
        
        if queue_size == 0:
            logger.info("Worker startup test PASSED")
            return True
        else:
            logger.error(f"Worker startup test FAILED: Queue still has {queue_size} tasks")
            return False
            
    except Exception as e:
        logger.error(f"Worker startup test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Queue Mechanism")
    logger.info("=" * 50)
    
    # Test 1: Basic queue
    logger.info("Test 1: Basic queue functionality")
    basic_test = test_basic_queue()
    
    # Test 2: Threaded queue
    logger.info("\nTest 2: Threaded queue functionality")
    threaded_test = test_threaded_queue()
    
    # Test 3: Crawler queue mechanism
    logger.info("\nTest 3: Crawler queue mechanism")
    crawler_test = test_crawler_queue_mechanism()
    
    # Test 4: Worker startup
    logger.info("\nTest 4: Worker startup")
    worker_test = test_worker_startup()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 30)
    logger.info(f"Basic queue: {'PASS' if basic_test else 'FAIL'}")
    logger.info(f"Threaded queue: {'PASS' if threaded_test else 'FAIL'}")
    logger.info(f"Crawler queue: {'PASS' if crawler_test else 'FAIL'}")
    logger.info(f"Worker startup: {'PASS' if worker_test else 'FAIL'}")
    
    total_passed = sum([basic_test, threaded_test, crawler_test, worker_test])
    
    if total_passed == 4:
        logger.info("\nALL TESTS PASSED!")
        logger.info("Queue mechanism is working correctly.")
    elif total_passed > 0:
        logger.info(f"\n{total_passed}/4 tests passed.")
        logger.info("Some queue functionality is working.")
    else:
        logger.error("\nALL TESTS FAILED!")
        logger.error("Queue mechanism has serious issues.")
    
    return total_passed > 0

if __name__ == "__main__":
    main()
