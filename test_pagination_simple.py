"""
Simple test for the fixed pagination functionality
"""

import time
import logging
from DrissionPage import ChromiumPage

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pagination_fix():
    """Test the fixed pagination logic"""
    page = None
    
    try:
        # Create page
        page = ChromiumPage()
        logger.info("✅ Browser created")
        
        # Test URL with known pagination
        test_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        logger.info(f"🌐 Testing: {test_url}")
        
        page.get(test_url)
        time.sleep(3)
        
        # Check for Cloudflare
        title = page.title.lower()
        if 'cloudflare' in title or 'checking' in title:
            logger.info("⏳ Waiting for Cloudflare...")
            time.sleep(10)
        
        original_url = page.url
        logger.info(f"📍 Original URL: {original_url}")
        
        # Count initial comments
        initial_comments = page.eles('.Comment')
        logger.info(f"💬 Initial comments: {len(initial_comments)}")
        
        # Look for pagination using the fixed logic
        max_page = 1
        last_page_link = None
        pagination_found = False
        
        # Method 1: Look for pager links
        try:
            pager_links = page.eles('.Pager a')
            if pager_links:
                logger.info(f"✅ Found {len(pager_links)} pager links")
                pagination_found = True
                
                for link in pager_links:
                    try:
                        link_text = link.text.strip()
                        link_href = link.attr('href') or ''
                        
                        logger.info(f"📄 Pager link: '{link_text}' -> {link_href}")
                        
                        if link_text.isdigit():
                            page_num = int(link_text)
                            if page_num > max_page:
                                max_page = page_num
                                last_page_link = link
                                logger.info(f"📈 New max page: {page_num}")
                        
                        # Check href for page numbers
                        import re
                        page_match = re.search(r'/p(\d+)', link_href)
                        if page_match:
                            page_num = int(page_match.group(1))
                            if page_num > max_page:
                                max_page = page_num
                                last_page_link = link
                                logger.info(f"📈 New max page from href: {page_num}")
                                
                    except Exception as e:
                        logger.debug(f"Error analyzing link: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"Error finding pager links: {e}")
        
        # Test navigation if pagination found
        if pagination_found and max_page > 1:
            logger.info(f"🚀 Attempting to navigate to page {max_page}")
            
            # Method 1: Try clicking the link
            if last_page_link:
                try:
                    link_text = last_page_link.text.strip()
                    link_href = last_page_link.attr('href') or ''
                    logger.info(f"🔗 Clicking: '{link_text}' -> {link_href}")
                    
                    last_page_link.click()
                    time.sleep(4)
                    
                    new_url = page.url
                    logger.info(f"📍 New URL: {new_url}")
                    
                    if new_url != original_url and f"/p{max_page}" in new_url:
                        logger.info("✅ Click navigation successful!")
                        
                        new_comments = page.eles('.Comment')
                        logger.info(f"💬 Comments on page {max_page}: {len(new_comments)}")
                        
                        return True
                    else:
                        logger.warning("⚠️ Click navigation failed")
                        
                except Exception as e:
                    logger.error(f"❌ Click failed: {e}")
            
            # Method 2: Try direct URL navigation
            logger.info("🔄 Trying direct URL navigation...")
            try:
                base_url = original_url.split('/p')[0]
                last_page_url = f"{base_url}/p{max_page}"
                
                logger.info(f"🌐 Direct navigation to: {last_page_url}")
                page.get(last_page_url)
                time.sleep(3)
                
                final_url = page.url
                logger.info(f"📍 Final URL: {final_url}")
                
                if f"/p{max_page}" in final_url:
                    logger.info("✅ Direct navigation successful!")
                    
                    final_comments = page.eles('.Comment')
                    logger.info(f"💬 Comments on page {max_page}: {len(final_comments)}")
                    
                    return True
                else:
                    logger.error("❌ Direct navigation failed")
                    
            except Exception as e:
                logger.error(f"❌ Direct navigation error: {e}")
        
        else:
            logger.warning("⚠️ No pagination found or only one page")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if page:
            try:
                page.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def main():
    """Main test function"""
    logger.info("🚀 Testing Fixed Pagination Logic")
    logger.info("=" * 40)
    
    success = test_pagination_fix()
    
    if success:
        logger.info("🎉 Pagination test PASSED!")
        logger.info("The fixed pagination logic is working correctly.")
    else:
        logger.error("💥 Pagination test FAILED!")
        logger.error("The pagination logic needs further debugging.")
    
    return success

if __name__ == "__main__":
    main()
