"""
Test the critical state management fixes
"""

import time
import logging
import json
import os
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def backup_current_state():
    """Backup current state file for safety"""
    state_file = "lowendtalk_crawl_state.json"
    if os.path.exists(state_file):
        backup_file = f"lowendtalk_crawl_state_backup_{int(time.time())}.json"
        import shutil
        shutil.copy2(state_file, backup_file)
        logger.info(f"Backed up state file to: {backup_file}")
        return backup_file
    return None

def check_current_state():
    """Check current state file"""
    logger.info("Checking current state file...")
    
    try:
        state_file = "lowendtalk_crawl_state.json"
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            processed_posts = state.get("processed_posts", {})
            logger.info(f"Current state has {len(processed_posts)} processed posts")
            
            # Show posts with their comment counts
            for i, (url, data) in enumerate(list(processed_posts.items())[:5]):
                comment_count = data.get("last_comment_count", 0)
                last_content = data.get("last_comment_content", "")[:50]
                last_processed = data.get("last_processed_at", "unknown")
                
                logger.info(f"  Post {i+1}: {url[:50]}...")
                logger.info(f"    Comments: {comment_count}")
                logger.info(f"    Last content: {last_content}...")
                logger.info(f"    Last processed: {last_processed}")
                logger.info("")
            
            return state
        else:
            logger.warning("State file does not exist")
            return None
            
    except Exception as e:
        logger.error(f"Failed to check current state: {e}")
        return None

def test_state_update_mechanism():
    """Test the state update mechanism"""
    logger.info("Testing state update mechanism...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test state update with mock data
        test_url = "https://lowendtalk.com/discussion/test/state-update"
        test_comment_count = 150
        test_datetime = datetime.now().isoformat()
        test_processed_ids = {"comment_1", "comment_2", "comment_3"}
        test_last_content = "This is the last comment content for testing incremental processing."
        
        logger.info(f"Testing state update with:")
        logger.info(f"  URL: {test_url}")
        logger.info(f"  Comment count: {test_comment_count}")
        logger.info(f"  Last content: {test_last_content[:50]}...")
        
        # Update state
        crawler._update_processed_post_state(
            test_url,
            test_comment_count,
            test_datetime,
            test_processed_ids,
            test_last_content
        )
        
        # Verify state was updated
        time.sleep(1)  # Brief delay
        current_state = crawler.state_manager.load_state()
        processed_posts = current_state.get("processed_posts", {})
        
        if test_url in processed_posts:
            saved_data = processed_posts[test_url]
            saved_count = saved_data.get("last_comment_count", 0)
            saved_content = saved_data.get("last_comment_content", "")
            
            if saved_count == test_comment_count and saved_content == test_last_content:
                logger.info("STATE UPDATE TEST PASSED!")
                logger.info(f"  Verified comment count: {saved_count}")
                logger.info(f"  Verified last content: {saved_content[:50]}...")
                return True
            else:
                logger.error(f"STATE UPDATE TEST FAILED!")
                logger.error(f"  Expected count: {test_comment_count}, got: {saved_count}")
                logger.error(f"  Expected content: {test_last_content[:50]}...")
                logger.error(f"  Got content: {saved_content[:50]}...")
                return False
        else:
            logger.error("STATE UPDATE TEST FAILED! Test URL not found in state")
            return False
            
    except Exception as e:
        logger.error(f"State update test failed: {e}")
        return False

def test_incremental_processing_logic():
    """Test incremental processing logic"""
    logger.info("Testing incremental processing logic...")
    
    # Mock comment data
    mock_comments = [
        {"id": "comment_1", "text": "This is the first comment"},
        {"id": "comment_2", "text": "This is the second comment"},
        {"id": "comment_3", "text": "This is the third comment"},
        {"id": "comment_4", "text": "This is the fourth comment"},
        {"id": "comment_5", "text": "This is the newest comment"},
    ]
    
    # Simulate last known comment (comment_3)
    last_known_content = "This is the third comment"
    
    # Test incremental processing logic
    comments_to_process = list(reversed(mock_comments))  # Start from newest
    processed_new_comments = 0
    new_comment_ids = []
    
    logger.info("Simulating incremental processing...")
    logger.info(f"Last known content: {last_known_content}")
    logger.info(f"Processing {len(comments_to_process)} comments from newest to oldest")
    
    for i, comment in enumerate(comments_to_process):
        comment_text = comment["text"]
        comment_id = comment["id"]
        
        logger.info(f"  Processing comment {i+1}: {comment_id} - {comment_text}")
        
        # Check if this is the last known comment
        if comment_text == last_known_content:
            logger.info(f"  FOUND last known comment! Stopping incremental processing")
            logger.info(f"  Processed {processed_new_comments} new comments")
            break
        
        # This is a new comment
        processed_new_comments += 1
        new_comment_ids.append(comment_id)
        logger.info(f"  -> NEW comment detected")
    
    expected_new_comments = 2  # comment_5 and comment_4
    expected_new_ids = ["comment_5", "comment_4"]
    
    if processed_new_comments == expected_new_comments and new_comment_ids == expected_new_ids:
        logger.info("INCREMENTAL PROCESSING TEST PASSED!")
        logger.info(f"  Correctly identified {processed_new_comments} new comments")
        logger.info(f"  New comment IDs: {new_comment_ids}")
        return True
    else:
        logger.error("INCREMENTAL PROCESSING TEST FAILED!")
        logger.error(f"  Expected {expected_new_comments} new comments, got {processed_new_comments}")
        logger.error(f"  Expected IDs: {expected_new_ids}, got: {new_comment_ids}")
        return False

def test_thread_safety():
    """Test thread safety of state updates"""
    logger.info("Testing thread safety...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        import threading
        import time
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test concurrent state updates
        test_url = "https://lowendtalk.com/discussion/test/thread-safety"
        results = []
        
        def worker_update(worker_id, comment_count):
            try:
                crawler._update_processed_post_state(
                    test_url,
                    comment_count,
                    datetime.now().isoformat(),
                    {f"comment_{worker_id}_{i}" for i in range(3)},
                    f"Last comment from worker {worker_id}"
                )
                results.append(f"Worker {worker_id} completed with count {comment_count}")
                logger.info(f"Worker {worker_id} completed")
            except Exception as e:
                results.append(f"Worker {worker_id} failed: {e}")
                logger.error(f"Worker {worker_id} failed: {e}")
        
        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker_update, args=(i, 100 + i))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check final state
        time.sleep(1)
        final_state = crawler.state_manager.load_state()
        if test_url in final_state.get("processed_posts", {}):
            final_count = final_state["processed_posts"][test_url]["last_comment_count"]
            logger.info(f"THREAD SAFETY TEST COMPLETED")
            logger.info(f"  Final comment count: {final_count}")
            logger.info(f"  All worker results: {results}")
            return True
        else:
            logger.error("THREAD SAFETY TEST FAILED - URL not found in final state")
            return False
            
    except Exception as e:
        logger.error(f"Thread safety test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Critical State Management Fixes")
    logger.info("=" * 60)
    
    # Backup current state
    backup_file = backup_current_state()
    
    # Check current state
    logger.info("Step 1: Checking current state")
    current_state = check_current_state()
    
    # Test state update mechanism
    logger.info("\nStep 2: Testing state update mechanism")
    state_update_test = test_state_update_mechanism()
    
    # Test incremental processing logic
    logger.info("\nStep 3: Testing incremental processing logic")
    incremental_test = test_incremental_processing_logic()
    
    # Test thread safety
    logger.info("\nStep 4: Testing thread safety")
    thread_safety_test = test_thread_safety()
    
    # Final check
    logger.info("\nStep 5: Final state check")
    final_state = check_current_state()
    
    # Summary
    logger.info("\nTEST RESULTS SUMMARY:")
    logger.info("=" * 40)
    logger.info(f"State update mechanism: {'PASS' if state_update_test else 'FAIL'}")
    logger.info(f"Incremental processing: {'PASS' if incremental_test else 'FAIL'}")
    logger.info(f"Thread safety: {'PASS' if thread_safety_test else 'FAIL'}")
    
    total_passed = sum([state_update_test, incremental_test, thread_safety_test])
    
    if total_passed == 3:
        logger.info("\nALL TESTS PASSED!")
        logger.info("State management fixes are working correctly.")
        logger.info("The crawler should now:")
        logger.info("  - Update comment counts correctly in state file")
        logger.info("  - Store last comment content for incremental processing")
        logger.info("  - Only process new comments, not all comments repeatedly")
        logger.info("  - Handle concurrent state updates safely")
    elif total_passed > 0:
        logger.info(f"\n{total_passed}/3 tests passed.")
        logger.info("Some fixes are working, but issues remain.")
    else:
        logger.error("\nALL TESTS FAILED!")
        logger.error("State management fixes need further debugging.")
    
    if backup_file:
        logger.info(f"\nOriginal state backed up to: {backup_file}")
    
    return total_passed == 3

if __name__ == "__main__":
    main()
