"""
Test script to verify refresh speed and pagination functionality
"""

import time
import json
import logging
from datetime import datetime
from improved_forum_crawler import CrawlerConfig, ForumCrawler

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config_loading():
    """Test if configuration is loaded correctly"""
    logger.info("🧪 Testing configuration loading...")
    
    try:
        # Load config from file
        config = CrawlerConfig.from_file("crawler_config.json")
        
        logger.info(f"✅ Configuration loaded successfully")
        logger.info(f"📊 refresh_interval: {config.refresh_interval} seconds")
        logger.info(f"📄 enable_pagination: {config.enable_pagination}")
        logger.info(f"📚 max_pages_to_check: {config.max_pages_to_check}")
        logger.info(f"💬 comments_per_page: {config.comments_per_page}")
        
        # Verify the values
        if config.refresh_interval == 1:
            logger.info("✅ Refresh interval correctly set to 1 second")
        else:
            logger.error(f"❌ Refresh interval is {config.refresh_interval}, expected 1")
            
        if config.enable_pagination:
            logger.info("✅ Pagination is enabled")
        else:
            logger.error("❌ Pagination is disabled")
            
        return config
        
    except Exception as e:
        logger.error(f"❌ Failed to load configuration: {e}")
        return None

def test_refresh_timing():
    """Test the actual refresh timing"""
    logger.info("\n🕐 Testing refresh timing...")
    
    config = test_config_loading()
    if not config:
        return False
    
    logger.info(f"⏱️ Expected refresh interval: {config.refresh_interval} seconds")
    logger.info("🔄 Simulating 3 refresh cycles...")
    
    start_time = time.time()
    
    for i in range(3):
        cycle_start = time.time()
        logger.info(f"📍 Cycle {i+1} started at {datetime.now().strftime('%H:%M:%S')}")
        
        # Simulate the refresh interval
        time.sleep(config.refresh_interval)
        
        cycle_end = time.time()
        actual_interval = cycle_end - cycle_start
        logger.info(f"⏰ Cycle {i+1} took {actual_interval:.2f} seconds")
    
    total_time = time.time() - start_time
    expected_time = config.refresh_interval * 3
    
    logger.info(f"📊 Total time: {total_time:.2f}s, Expected: {expected_time:.2f}s")
    
    if abs(total_time - expected_time) < 0.5:  # Allow 0.5s tolerance
        logger.info("✅ Refresh timing is working correctly")
        return True
    else:
        logger.error("❌ Refresh timing is not working as expected")
        return False

def test_pagination_config():
    """Test pagination configuration"""
    logger.info("\n📄 Testing pagination configuration...")
    
    config = test_config_loading()
    if not config:
        return False
    
    # Check if pagination attributes exist
    pagination_attrs = ['enable_pagination', 'max_pages_to_check', 'comments_per_page']
    
    for attr in pagination_attrs:
        if hasattr(config, attr):
            value = getattr(config, attr)
            logger.info(f"✅ {attr}: {value}")
        else:
            logger.error(f"❌ Missing pagination attribute: {attr}")
            return False
    
    # Verify pagination is enabled
    if config.enable_pagination:
        logger.info("✅ Pagination functionality is enabled")
        return True
    else:
        logger.error("❌ Pagination functionality is disabled")
        return False

def show_current_config():
    """Show current configuration file contents"""
    logger.info("\n📋 Current configuration file contents:")
    
    try:
        with open('crawler_config.json', 'r') as f:
            config_data = json.load(f)
        
        # Show relevant settings
        relevant_keys = [
            'refresh_interval', 
            'enable_pagination', 
            'max_pages_to_check', 
            'comments_per_page',
            'min_request_delay',
            'max_request_delay'
        ]
        
        for key in relevant_keys:
            if key in config_data:
                logger.info(f"  {key}: {config_data[key]}")
            else:
                logger.warning(f"  {key}: NOT SET")
                
    except Exception as e:
        logger.error(f"❌ Could not read config file: {e}")

def create_speed_test_config():
    """Create a test configuration with fast refresh"""
    logger.info("\n⚡ Creating speed test configuration...")
    
    speed_config = {
        "base_url": "https://lowendtalk.com/",
        "monitor_url": "https://lowendtalk.com/categories/offers",
        "state_file": "test_crawl_state.json",
        "results_file": "test_flash_sales.json",
        
        "flash_sale_keywords": [
            "offer", "sale", "discount", "promo", "limited", "flash sale",
            "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
            "$/month", "$/yr", "$/year", "$/mo", "price", "deal", "special", "promotion"
        ],
        
        "num_workers": 2,  # Reduced for testing
        "monitor_timeout": 90,
        "worker_timeout": 30,
        "queue_timeout": 30,
        
        "headless": False,  # For Cloudflare bypass
        "user_agents": [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ],
        
        # SPEED SETTINGS
        "refresh_interval": 1,      # 1 second refresh
        "scroll_delay": 1.0,        # Faster scrolling
        "page_load_delay": 2.0,     # Faster page loading
        
        "min_request_delay": 0.5,   # Faster requests
        "max_request_delay": 1.5,
        
        # PAGINATION SETTINGS
        "enable_pagination": True,
        "max_pages_to_check": 3,
        "comments_per_page": 25,
        
        "max_posts_to_check": 3,    # Reduced for testing
        "max_retries": 3
    }
    
    try:
        with open('speed_test_config.json', 'w') as f:
            json.dump(speed_config, f, indent=2)
        
        logger.info("✅ Speed test configuration created: speed_test_config.json")
        logger.info("🚀 Key settings:")
        logger.info(f"   - refresh_interval: {speed_config['refresh_interval']} seconds")
        logger.info(f"   - enable_pagination: {speed_config['enable_pagination']}")
        logger.info(f"   - max_pages_to_check: {speed_config['max_pages_to_check']}")
        logger.info(f"   - num_workers: {speed_config['num_workers']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create speed test config: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🚀 Speed and Pagination Test Suite")
    logger.info("=" * 50)
    
    # Show current config
    show_current_config()
    
    # Test configuration loading
    config_ok = test_config_loading()
    
    # Test refresh timing
    timing_ok = test_refresh_timing()
    
    # Test pagination config
    pagination_ok = test_pagination_config()
    
    # Create speed test config
    speed_config_ok = create_speed_test_config()
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    logger.info(f"   Configuration Loading: {'✅ PASS' if config_ok else '❌ FAIL'}")
    logger.info(f"   Refresh Timing: {'✅ PASS' if timing_ok else '❌ FAIL'}")
    logger.info(f"   Pagination Config: {'✅ PASS' if pagination_ok else '❌ FAIL'}")
    logger.info(f"   Speed Test Config: {'✅ PASS' if speed_config_ok else '❌ FAIL'}")
    
    all_passed = all([config_ok, timing_ok, pagination_ok, speed_config_ok])
    
    if all_passed:
        logger.info("\n🎉 All tests passed! Your configuration should work correctly.")
        logger.info("\n📝 Next steps:")
        logger.info("1. Run: python improved_forum_crawler.py")
        logger.info("2. Or use speed test config: python improved_forum_crawler.py --config speed_test_config.json")
    else:
        logger.error("\n💥 Some tests failed. Please check the configuration.")
    
    return all_passed

if __name__ == "__main__":
    main()
