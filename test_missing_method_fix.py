"""
Test the missing method fix for the real-time crawler
"""

import time
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_missing_methods():
    """Test if the missing methods are now available"""
    logger.info("Testing missing method fix...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test if the missing methods exist
        missing_methods = []
        required_methods = [
            '_extract_posts_from_page',
            '_extract_post_data',
            '_detect_comment_changes',
            '_analyze_post_changes',
            '_should_process_new_post',
            '_queue_for_processing',
            '_record_new_post_in_state'
        ]
        
        for method_name in required_methods:
            if hasattr(crawler, method_name):
                logger.info(f"✅ Method exists: {method_name}")
            else:
                logger.error(f"❌ Method missing: {method_name}")
                missing_methods.append(method_name)
        
        if missing_methods:
            logger.error(f"Missing methods: {missing_methods}")
            return False
        else:
            logger.info("All required methods are present")
            return True
            
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

def test_post_extraction_logic():
    """Test the post extraction logic with mock data"""
    logger.info("Testing post extraction logic...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test change analysis
        test_cases = [
            # (post_data, processed_posts, expected_result)
            (
                {"post_url": "https://test.com/new", "current_comment_count": 10},
                {},
                "new_post"
            ),
            (
                {"post_url": "https://test.com/existing", "current_comment_count": 25},
                {"https://test.com/existing": {"last_comment_count": 20}},
                "comment_increase"
            ),
            (
                {"post_url": "https://test.com/same", "current_comment_count": 15},
                {"https://test.com/same": {"last_comment_count": 15}},
                "no_change"
            )
        ]
        
        for post_data, processed_posts, expected in test_cases:
            result = crawler._analyze_post_changes(post_data, processed_posts)
            if result == expected:
                logger.info(f"✅ Change analysis: {expected}")
            else:
                logger.error(f"❌ Change analysis failed: Expected {expected}, got {result}")
                return False
        
        # Test new post filtering
        filter_cases = [
            ({"current_comment_count": 3}, False),
            ({"current_comment_count": 5}, True),
            ({"current_comment_count": 10}, True)
        ]
        
        for post_data, expected in filter_cases:
            result = crawler._should_process_new_post(post_data)
            if result == expected:
                logger.info(f"✅ New post filter: {post_data['current_comment_count']} comments -> {result}")
            else:
                logger.error(f"❌ New post filter failed: Expected {expected}, got {result}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Post extraction logic test failed: {e}")
        return False

def test_real_time_monitoring_startup():
    """Test if real-time monitoring can start without errors"""
    logger.info("Testing real-time monitoring startup...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(
            monitor_interval=10,
            num_workers=1,
            headless=True
        )
        
        crawler = ForumCrawler(config)
        
        # Test initialization
        if hasattr(crawler, 'monitoring_active'):
            logger.info("✅ Monitoring system initialized")
        else:
            logger.error("❌ Monitoring system not initialized")
            return False
        
        # Test configuration
        if crawler.monitor_interval == 10:
            logger.info(f"✅ Monitor interval: {crawler.monitor_interval}s")
        else:
            logger.error(f"❌ Monitor interval incorrect: {crawler.monitor_interval}")
            return False
        
        # Test queue initialization
        if hasattr(crawler, 'change_detection_queue'):
            logger.info("✅ Change detection queue initialized")
        else:
            logger.error("❌ Change detection queue missing")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Real-time monitoring startup test failed: {e}")
        return False

def test_browser_manager_compatibility():
    """Test if browser manager is compatible with new system"""
    logger.info("Testing browser manager compatibility...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, BrowserManager
        
        config = CrawlerConfig(headless=True)
        browser_manager = BrowserManager(config)
        
        # Test browser manager methods
        required_methods = [
            'start_browser',
            'create_tab',
            'handle_cloudflare_verification',
            'cleanup'
        ]
        
        for method_name in required_methods:
            if hasattr(browser_manager, method_name):
                logger.info(f"✅ BrowserManager method: {method_name}")
            else:
                logger.error(f"❌ BrowserManager method missing: {method_name}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Browser manager compatibility test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Missing Method Fix")
    logger.info("=" * 40)
    
    # Test 1: Missing methods
    logger.info("Test 1: Missing methods check")
    methods_test = test_missing_methods()
    
    # Test 2: Post extraction logic
    logger.info("\nTest 2: Post extraction logic")
    extraction_test = test_post_extraction_logic()
    
    # Test 3: Real-time monitoring startup
    logger.info("\nTest 3: Real-time monitoring startup")
    startup_test = test_real_time_monitoring_startup()
    
    # Test 4: Browser manager compatibility
    logger.info("\nTest 4: Browser manager compatibility")
    browser_test = test_browser_manager_compatibility()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 30)
    logger.info(f"Missing methods: {'PASS' if methods_test else 'FAIL'}")
    logger.info(f"Extraction logic: {'PASS' if extraction_test else 'FAIL'}")
    logger.info(f"Monitoring startup: {'PASS' if startup_test else 'FAIL'}")
    logger.info(f"Browser compatibility: {'PASS' if browser_test else 'FAIL'}")
    
    total_passed = sum([methods_test, extraction_test, startup_test, browser_test])
    
    if total_passed == 4:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("Missing method fix is working correctly.")
        logger.info("The real-time crawler should now run without errors.")
        logger.info("\nReady to run: python improved_forum_crawler.py")
    elif total_passed > 0:
        logger.info(f"\n⚠️ {total_passed}/4 tests passed.")
        logger.info("Some functionality is working, but issues remain.")
    else:
        logger.error("\n💥 ALL TESTS FAILED!")
        logger.error("Missing method fix needs further debugging.")
    
    return total_passed == 4

if __name__ == "__main__":
    main()
