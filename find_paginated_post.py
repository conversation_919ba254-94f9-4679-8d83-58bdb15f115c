"""
Find a post with actual pagination for testing
"""

import time
import logging
import re
from DrissionPage import ChromiumPage

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_paginated_post():
    """Find a post with multiple pages"""
    page = None
    
    try:
        page = ChromiumPage()
        logger.info("✅ Browser created")
        
        # Start with the offers page to find posts with many comments
        logger.info("🔍 Searching for posts with many comments...")
        page.get("https://lowendtalk.com/categories/offers")
        time.sleep(5)
        
        # Check for Cloudflare
        title = page.title.lower()
        if 'cloudflare' in title or 'checking' in title:
            logger.info("⏳ Waiting for Cloudflare...")
            time.sleep(15)
        
        logger.info(f"📄 Page title: {page.title}")
        
        # Find posts with high comment counts
        posts = page.eles('.ItemDiscussion')
        if not posts:
            posts = page.eles('.Item')
        
        logger.info(f"📋 Found {len(posts)} posts")
        
        potential_posts = []
        
        for i, post in enumerate(posts[:20]):  # Check first 20 posts
            try:
                # Get post title and link
                title_link = post.ele('a')
                if not title_link:
                    continue
                
                post_title = title_link.text.strip()
                post_url = title_link.link
                
                # Look for comment count
                comment_count = 0
                comment_elements = post.eles('.MItem')
                
                for elem in comment_elements:
                    text = elem.text.strip()
                    # Look for patterns like "123 comments" or "45 replies"
                    match = re.search(r'(\d+)\s*(comment|reply)', text.lower())
                    if match:
                        comment_count = int(match.group(1))
                        break
                
                if comment_count > 50:  # Likely to have multiple pages
                    potential_posts.append({
                        'title': post_title,
                        'url': post_url,
                        'comments': comment_count
                    })
                    logger.info(f"📄 Found potential post: {post_title[:50]}... ({comment_count} comments)")
                
            except Exception as e:
                logger.debug(f"Error checking post {i}: {e}")
                continue
        
        # Test the most promising posts
        for post_info in potential_posts[:3]:  # Test top 3
            logger.info(f"\n🧪 Testing post: {post_info['title'][:50]}...")
            logger.info(f"🔗 URL: {post_info['url']}")
            
            # Navigate to the post
            page.get(post_info['url'])
            time.sleep(3)
            
            # Check for pagination
            has_pagination = False
            
            # Test multiple selectors
            pagination_selectors = [
                '.Pager',
                '.Pager a',
                'a[href*="/p2"]',
                'a[href*="/p3"]',
                'a:contains("2")',
                'a:contains("Next")',
                'a:contains("»")',
            ]
            
            for selector in pagination_selectors:
                try:
                    elements = page.eles(selector)
                    if elements:
                        logger.info(f"✅ Found pagination with {selector}: {len(elements)} elements")
                        
                        # Show some examples
                        for elem in elements[:3]:
                            text = elem.text.strip()
                            href = elem.attr('href') or ''
                            logger.info(f"   - '{text}' -> {href}")
                        
                        has_pagination = True
                        break
                except:
                    continue
            
            if has_pagination:
                logger.info(f"🎉 Found paginated post: {post_info['url']}")
                return post_info['url']
            else:
                logger.info("❌ No pagination found on this post")
        
        # If no paginated posts found in offers, try a different approach
        logger.info("\n🔍 Trying alternative approach...")
        
        # Try some known long-running discussions
        test_urls = [
            "https://lowendtalk.com/discussion/137719/lowendtalk-community-rules",
            "https://lowendtalk.com/discussion/153228/rules-for-selling-on-lowendtalk-updated-04th-sep-2018",
            "https://lowendtalk.com/discussion/185000/test-pagination",  # This might not exist
        ]
        
        for test_url in test_urls:
            logger.info(f"\n🧪 Testing known URL: {test_url}")
            
            try:
                page.get(test_url)
                time.sleep(3)
                
                # Check if page exists and has pagination
                if "404" not in page.title and "Not Found" not in page.title:
                    # Look for pagination
                    pager_elements = page.eles('.Pager a')
                    if pager_elements:
                        logger.info(f"✅ Found pagination: {len(pager_elements)} elements")
                        return test_url
                    else:
                        logger.info("❌ No pagination found")
                else:
                    logger.info("❌ Page not found")
                    
            except Exception as e:
                logger.error(f"Error testing {test_url}: {e}")
        
        logger.warning("⚠️ Could not find a post with pagination")
        return None
        
    except Exception as e:
        logger.error(f"❌ Search failed: {e}")
        return None
    finally:
        if page:
            try:
                page.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def test_pagination_on_found_post(post_url):
    """Test pagination on the found post"""
    page = None
    
    try:
        page = ChromiumPage()
        logger.info(f"🧪 Testing pagination on: {post_url}")
        
        page.get(post_url)
        time.sleep(3)
        
        # Look for pagination elements
        pager_links = page.eles('.Pager a')
        if pager_links:
            logger.info(f"✅ Found {len(pager_links)} pager links")
            
            # Find highest page number
            max_page = 1
            for link in pager_links:
                text = link.text.strip()
                if text.isdigit():
                    page_num = int(text)
                    if page_num > max_page:
                        max_page = page_num
            
            logger.info(f"📊 Highest page number: {max_page}")
            
            if max_page > 1:
                # Try to navigate to page 2
                for link in pager_links:
                    if link.text.strip() == "2":
                        logger.info("🔄 Clicking page 2...")
                        original_url = page.url
                        
                        link.click()
                        time.sleep(3)
                        
                        new_url = page.url
                        if new_url != original_url:
                            logger.info(f"✅ Navigation successful: {new_url}")
                            return True
                        else:
                            logger.warning("⚠️ URL didn't change")
                        break
        
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if page:
            try:
                page.quit()
            except:
                pass

def main():
    """Main function"""
    logger.info("🚀 Finding Paginated Post")
    logger.info("=" * 40)
    
    # Find a post with pagination
    paginated_url = find_paginated_post()
    
    if paginated_url:
        logger.info(f"\n🎉 Found paginated post: {paginated_url}")
        
        # Test pagination on this post
        logger.info("\n🧪 Testing pagination functionality...")
        success = test_pagination_on_found_post(paginated_url)
        
        if success:
            logger.info("✅ Pagination test PASSED!")
            logger.info(f"Use this URL for testing: {paginated_url}")
        else:
            logger.error("❌ Pagination test FAILED!")
    else:
        logger.error("💥 Could not find a suitable paginated post")
    
    return paginated_url is not None

if __name__ == "__main__":
    main()
