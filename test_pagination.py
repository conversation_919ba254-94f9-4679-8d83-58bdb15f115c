"""
Test script for pagination functionality
This script tests if the crawler can properly navigate through forum post pages to find new comments
"""

import time
import logging
from DrissionPage import ChromiumPage, Chromium, ChromiumOptions
from DrissionPage.common import Settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_browser():
    """Create a browser for testing"""
    try:
        Settings.set_singleton_tab_obj(False)
        
        options = ChromiumOptions()
        
        # Basic stealth settings
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # User agent
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
        options.add_argument(f'--user-agent={user_agent}')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        browser = Chromium(addr_or_opts=options)
        logger.info("✅ Test browser created")
        return browser
        
    except Exception as e:
        logger.error(f"❌ Failed to create test browser: {e}")
        return None

def test_pagination_on_post(post_url):
    """Test pagination functionality on a specific post"""
    browser = None
    
    try:
        browser = create_test_browser()
        if not browser:
            return False
        
        page = browser.new_tab()
        
        logger.info(f"🌐 Navigating to post: {post_url}")
        page.get(post_url)
        time.sleep(3)
        
        # Check initial page
        initial_comments = page.eles('.Comment')
        logger.info(f"📋 Found {len(initial_comments)} comments on initial page")
        
        # Look for pagination
        pagination_found = False
        pagination_selectors = [
            '.Pager a',
            '.PageNavigation a', 
            '.pager a',
            'a[href*="p2"]',
            'a[href*="page"]'
        ]
        
        for selector in pagination_selectors:
            try:
                page_links = page.eles(selector)
                if page_links:
                    logger.info(f"✅ Found pagination using selector: {selector}")
                    logger.info(f"📄 Found {len(page_links)} page links")
                    
                    # Show page numbers found
                    page_numbers = []
                    for link in page_links:
                        text = link.text.strip()
                        href = link.attr('href') or ''
                        if text.isdigit():
                            page_numbers.append(text)
                        elif 'p' in href:
                            import re
                            match = re.search(r'p(\d+)', href)
                            if match:
                                page_numbers.append(match.group(1))
                    
                    if page_numbers:
                        logger.info(f"📊 Page numbers found: {sorted(set(page_numbers))}")
                        pagination_found = True
                        
                        # Try to go to last page
                        max_page = max([int(p) for p in page_numbers if p.isdigit()])
                        if max_page > 1:
                            logger.info(f"🔄 Attempting to navigate to page {max_page}")
                            
                            # Find link to last page
                            for link in page_links:
                                if link.text.strip() == str(max_page):
                                    link.click()
                                    time.sleep(3)
                                    
                                    # Check comments on last page
                                    last_page_comments = page.eles('.Comment')
                                    logger.info(f"📋 Found {len(last_page_comments)} comments on last page")
                                    
                                    # Compare comment IDs to see if they're different
                                    if last_page_comments and initial_comments:
                                        initial_ids = [c.attr('id') for c in initial_comments[:3]]
                                        last_ids = [c.attr('id') for c in last_page_comments[:3]]
                                        
                                        if initial_ids != last_ids:
                                            logger.info("✅ Successfully navigated to different page with different comments")
                                        else:
                                            logger.warning("⚠️ Same comments found on both pages")
                                    
                                    break
                    break
                    
            except Exception as e:
                logger.debug(f"Selector {selector} failed: {e}")
                continue
        
        if not pagination_found:
            logger.info("ℹ️ No pagination found - this post may have only one page")
        
        # Test "Last" link navigation
        last_link_selectors = [
            'a[title*="Last"]',
            'a[title*="last"]', 
            'a:contains("Last")',
            'a:contains("»")'
        ]
        
        for selector in last_link_selectors:
            try:
                last_link = page.ele(selector)
                if last_link:
                    logger.info(f"✅ Found 'Last' link using selector: {selector}")
                    logger.info(f"🔗 Link text: '{last_link.text}', href: {last_link.attr('href')}")
                    break
            except:
                continue
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if browser:
            try:
                browser.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def test_with_sample_posts():
    """Test pagination with some sample LowEndTalk posts"""
    
    # You can replace these with actual post URLs that have multiple pages
    sample_posts = [
        "https://lowendtalk.com/discussion/185000/test-post",  # Replace with real URLs
        # Add more URLs here for testing
    ]
    
    logger.info("🧪 Testing pagination functionality")
    logger.info("=" * 50)
    
    if not sample_posts or sample_posts[0].endswith("test-post"):
        logger.warning("⚠️ No real post URLs provided for testing")
        logger.info("To test pagination:")
        logger.info("1. Find a LowEndTalk post with multiple pages of comments")
        logger.info("2. Replace the sample_posts URLs in this script")
        logger.info("3. Run the test again")
        return False
    
    success_count = 0
    for i, post_url in enumerate(sample_posts, 1):
        logger.info(f"\n🔍 Testing post {i}/{len(sample_posts)}")
        if test_pagination_on_post(post_url):
            success_count += 1
        time.sleep(2)  # Brief pause between tests
    
    logger.info(f"\n📊 Test Results: {success_count}/{len(sample_posts)} posts tested successfully")
    return success_count == len(sample_posts)

def main():
    """Main test function"""
    logger.info("🚀 Pagination Test Script")
    logger.info("This script tests the forum pagination functionality")
    
    # For now, just test the pagination detection logic
    logger.info("\n📝 Testing pagination detection logic...")
    
    # You can manually test by providing a specific post URL
    test_url = input("Enter a LowEndTalk post URL to test (or press Enter to skip): ").strip()
    
    if test_url:
        success = test_pagination_on_post(test_url)
        if success:
            logger.info("🎉 Pagination test completed successfully!")
        else:
            logger.error("💥 Pagination test failed!")
        return success
    else:
        logger.info("ℹ️ No URL provided, skipping test")
        logger.info("To test pagination functionality:")
        logger.info("1. Find a LowEndTalk post with many comments (multiple pages)")
        logger.info("2. Run this script again with that URL")
        return True

if __name__ == "__main__":
    main()
