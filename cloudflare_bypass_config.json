{"browser_settings": {"headless": false, "window_size": "1920,1080", "disable_images": false, "disable_javascript": false, "user_data_dir": "./chrome_user_data", "profile_directory": "<PERSON><PERSON><PERSON>"}, "chrome_arguments": ["--disable-blink-features=AutomationControlled", "--disable-dev-shm-usage", "--no-sandbox", "--disable-gpu", "--disable-extensions", "--disable-plugins", "--disable-web-security", "--disable-features=VizDisplayCompositor", "--disable-ipc-flooding-protection", "--disable-renderer-backgrounding", "--disable-backgrounding-occluded-windows", "--disable-client-side-phishing-detection", "--disable-sync", "--disable-default-apps", "--disable-component-extensions-with-background-pages", "--disable-background-timer-throttling", "--disable-background-networking", "--disable-hang-monitor", "--disable-prompt-on-repost", "--disable-domain-reliability", "--disable-component-update", "--window-size=1920,1080", "--start-maximized", "--no-first-run", "--no-default-browser-check", "--disable-infobars", "--disable-notifications"], "experimental_options": {"excludeSwitches": ["enable-automation", "enable-logging"], "useAutomationExtension": false, "prefs": {"profile.default_content_setting_values": {"notifications": 2, "geolocation": 2, "media_stream": 2}, "profile.managed_default_content_settings": {"images": 1}, "profile.default_content_settings": {"popups": 0}}}, "cloudflare_detection": {"indicators": ["Checking your browser before accessing", "DDoS protection by Cloudflare", "Please wait while we verify", "cf-browser-verification", "cf-challenge-running", "Cloudflare", "Just a moment", "Verifying you are human"], "max_wait_time": 60, "check_interval": 2}, "anti_detection": {"random_delays": {"min": 1.0, "max": 3.0}, "viewport_sizes": [[1920, 1080], [1366, 768], [1440, 900], [1536, 864]], "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"]}}