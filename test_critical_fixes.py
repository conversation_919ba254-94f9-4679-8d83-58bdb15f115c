"""
Test the critical fixes for new post processing and state persistence
"""

import time
import logging
import json
import os
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_new_post_filtering():
    """Test new post filtering with minimum comment threshold"""
    logger.info("Testing new post filtering...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(min_comments_for_new_posts=5)
        crawler = ForumCrawler(config)
        
        # Test cases for new posts
        test_cases = [
            # (comment_count, should_process, description)
            (3, False, "New post with 3 comments should be skipped"),
            (4, <PERSON>alse, "New post with 4 comments should be skipped"),
            (5, True, "New post with 5 comments should be processed"),
            (10, True, "New post with 10 comments should be processed"),
        ]
        
        # Empty processed_posts (simulating new posts)
        processed_posts = {}
        
        for comment_count, expected, description in test_cases:
            post_data = {
                "post_url": f"https://lowendtalk.com/discussion/test/{comment_count}",
                "post_title": f"Test Post with {comment_count} Comments",
                "current_comment_count": comment_count,
                "datetime_attr": datetime.now().isoformat()
            }
            
            result = crawler._should_process_post(post_data, processed_posts)
            
            if result == expected:
                logger.info(f"✅ {description}: {result}")
            else:
                logger.error(f"❌ {description}: Expected {expected}, got {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"New post filtering test failed: {e}")
        return False

def test_existing_post_processing():
    """Test existing post processing logic"""
    logger.info("Testing existing post processing...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig(min_comments_for_new_posts=5)
        crawler = ForumCrawler(config)
        
        # Simulate existing post in state
        processed_posts = {
            "https://lowendtalk.com/discussion/test/existing": {
                "last_comment_count": 758,
                "last_comment_datetime": "2024-07-14T20:00:00Z",
                "processed_comment_ids": ["comment1", "comment2"]
            }
        }
        
        test_cases = [
            # (current_count, expected, description)
            (758, False, "Same comment count should not be processed"),
            (759, True, "Comment increase from 758 to 759 should be processed"),
            (760, True, "Comment increase from 758 to 760 should be processed"),
            (757, False, "Comment decrease should not be processed"),
        ]
        
        for current_count, expected, description in test_cases:
            post_data = {
                "post_url": "https://lowendtalk.com/discussion/test/existing",
                "post_title": "Existing Test Post",
                "current_comment_count": current_count,
                "datetime_attr": "2024-07-14T21:00:00Z"
            }
            
            result = crawler._should_process_post(post_data, processed_posts)
            
            if result == expected:
                logger.info(f"✅ {description}: {result}")
            else:
                logger.error(f"❌ {description}: Expected {expected}, got {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"Existing post processing test failed: {e}")
        return False

def test_state_persistence():
    """Test state file persistence"""
    logger.info("Testing state file persistence...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test state update with verification
        test_url = "https://lowendtalk.com/discussion/test/persistence"
        test_comment_count = 760  # Simulate the birthday post current count
        test_datetime = datetime.now().isoformat()
        test_processed_ids = {"comment_1", "comment_2", "comment_3"}
        test_last_content = "This is the last comment for persistence testing."
        
        logger.info(f"Testing state persistence with:")
        logger.info(f"  URL: {test_url}")
        logger.info(f"  Comment count: {test_comment_count}")
        
        # Update state
        crawler._update_processed_post_state(
            test_url,
            test_comment_count,
            test_datetime,
            test_processed_ids,
            test_last_content
        )
        
        # Wait for file system operations
        time.sleep(1)
        
        # Verify by reading the JSON file directly
        state_file = "lowendtalk_crawl_state.json"
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                file_state = json.load(f)
            
            processed_posts = file_state.get("processed_posts", {})
            if test_url in processed_posts:
                saved_count = processed_posts[test_url].get("last_comment_count", 0)
                saved_content = processed_posts[test_url].get("last_comment_content", "")
                saved_status = processed_posts[test_url].get("status", "unknown")
                
                if saved_count == test_comment_count and saved_content == test_last_content:
                    logger.info("✅ State persistence test PASSED!")
                    logger.info(f"  File contains correct count: {saved_count}")
                    logger.info(f"  File contains correct content: {saved_content[:50]}...")
                    logger.info(f"  Status: {saved_status}")
                    return True
                else:
                    logger.error("❌ State persistence test FAILED!")
                    logger.error(f"  Expected count: {test_comment_count}, file has: {saved_count}")
                    logger.error(f"  Expected content: {test_last_content[:50]}...")
                    logger.error(f"  File has content: {saved_content[:50]}...")
                    return False
            else:
                logger.error("❌ State persistence test FAILED! Test URL not found in file")
                return False
        else:
            logger.error("❌ State persistence test FAILED! State file not found")
            return False
            
    except Exception as e:
        logger.error(f"State persistence test failed: {e}")
        return False

def check_birthday_post_state():
    """Check the current state of the birthday post"""
    logger.info("Checking birthday post state...")
    
    try:
        state_file = "lowendtalk_crawl_state.json"
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            processed_posts = state.get("processed_posts", {})
            
            # Look for birthday post
            birthday_post = None
            for url, data in processed_posts.items():
                if "15th-birthday" in url.lower() or "birthday" in url.lower():
                    birthday_post = (url, data)
                    break
            
            if birthday_post:
                url, data = birthday_post
                comment_count = data.get("last_comment_count", 0)
                status = data.get("status", "unknown")
                last_processed = data.get("last_processed_at", "unknown")
                
                logger.info(f"Birthday post current state:")
                logger.info(f"  URL: {url}")
                logger.info(f"  Comment count in state: {comment_count}")
                logger.info(f"  Status: {status}")
                logger.info(f"  Last processed: {last_processed}")
                
                if comment_count >= 760:
                    logger.info("✅ Birthday post state looks current")
                    return True
                else:
                    logger.warning(f"⚠️ Birthday post state may be outdated (showing {comment_count}, expected ≥760)")
                    return False
            else:
                logger.warning("⚠️ Birthday post not found in state")
                return False
        else:
            logger.warning("⚠️ State file does not exist")
            return False
            
    except Exception as e:
        logger.error(f"Failed to check birthday post state: {e}")
        return False

def test_config_loading():
    """Test configuration loading with new settings"""
    logger.info("Testing configuration loading...")
    
    try:
        from improved_forum_crawler import CrawlerConfig
        
        # Test loading from file
        config = CrawlerConfig.from_file("crawler_config.json")
        
        if hasattr(config, 'min_comments_for_new_posts'):
            logger.info(f"✅ min_comments_for_new_posts loaded: {config.min_comments_for_new_posts}")
            return True
        else:
            logger.error("❌ min_comments_for_new_posts not found in config")
            return False
            
    except Exception as e:
        logger.error(f"Config loading test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Critical Fixes")
    logger.info("=" * 50)
    
    # Test 1: Configuration loading
    logger.info("Test 1: Configuration loading")
    config_test = test_config_loading()
    
    # Test 2: New post filtering
    logger.info("\nTest 2: New post filtering")
    new_post_test = test_new_post_filtering()
    
    # Test 3: Existing post processing
    logger.info("\nTest 3: Existing post processing")
    existing_post_test = test_existing_post_processing()
    
    # Test 4: State persistence
    logger.info("\nTest 4: State persistence")
    persistence_test = test_state_persistence()
    
    # Test 5: Check birthday post state
    logger.info("\nTest 5: Birthday post state check")
    birthday_test = check_birthday_post_state()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 30)
    logger.info(f"Configuration loading: {'PASS' if config_test else 'FAIL'}")
    logger.info(f"New post filtering: {'PASS' if new_post_test else 'FAIL'}")
    logger.info(f"Existing post processing: {'PASS' if existing_post_test else 'FAIL'}")
    logger.info(f"State persistence: {'PASS' if persistence_test else 'FAIL'}")
    logger.info(f"Birthday post state: {'PASS' if birthday_test else 'FAIL'}")
    
    total_passed = sum([config_test, new_post_test, existing_post_test, persistence_test, birthday_test])
    
    if total_passed == 5:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("Critical fixes are working correctly:")
        logger.info("  ✅ New posts with <5 comments will be skipped")
        logger.info("  ✅ State file updates are persisting correctly")
        logger.info("  ✅ Comment count tracking is accurate")
    elif total_passed > 0:
        logger.info(f"\n⚠️ {total_passed}/5 tests passed.")
        logger.info("Some fixes are working, but issues remain.")
    else:
        logger.error("\n💥 ALL TESTS FAILED!")
        logger.error("Critical fixes need further debugging.")
    
    return total_passed == 5

if __name__ == "__main__":
    main()
