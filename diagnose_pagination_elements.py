"""
Detailed diagnosis of pagination elements
"""

import time
import logging
import re
from DrissionPage import ChromiumPage

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def diagnose_pagination():
    """Diagnose pagination elements in detail"""
    page = None
    
    try:
        page = ChromiumPage()
        logger.info("✅ Browser created")
        
        test_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        logger.info(f"🌐 Loading: {test_url}")
        
        page.get(test_url)
        time.sleep(5)  # Wait longer for full load
        
        # Check for Cloudflare
        title = page.title.lower()
        if 'cloudflare' in title or 'checking' in title:
            logger.info("⏳ Waiting for Cloudflare...")
            time.sleep(15)
        
        logger.info(f"📄 Page title: {page.title}")
        logger.info(f"📍 Current URL: {page.url}")
        
        # Test all possible selectors
        selectors_to_test = [
            '.Pager',
            '.Pager a',
            '.NumberedPager',
            '.PagerLinkCount',
            '.Pager-p',
            'a[class*="Pager"]',
            'a[href*="/p"]',
            'a[href*="/p2"]',
            'a[href*="/p3"]',
            '[class*="pager"]',
            '[class*="Pager"]',
        ]
        
        logger.info("🔍 Testing selectors...")
        
        for selector in selectors_to_test:
            try:
                elements = page.eles(selector)
                if elements:
                    logger.info(f"✅ {selector}: Found {len(elements)} elements")
                    
                    # Show details of first few elements
                    for i, elem in enumerate(elements[:3]):
                        try:
                            text = elem.text.strip()
                            href = elem.attr('href') or ''
                            classes = elem.attr('class') or ''
                            
                            logger.info(f"   Element {i+1}:")
                            logger.info(f"     Text: '{text}'")
                            logger.info(f"     Href: '{href}'")
                            logger.info(f"     Classes: '{classes}'")
                            
                        except Exception as e:
                            logger.warning(f"     Error getting element details: {e}")
                else:
                    logger.info(f"❌ {selector}: No elements found")
                    
            except Exception as e:
                logger.error(f"❌ {selector}: Error - {e}")
        
        # Check HTML source for pagination patterns
        logger.info("\n🔍 Checking HTML source...")
        try:
            html = page.html
            
            # Look for pagination patterns
            patterns = [
                r'class="[^"]*Pager[^"]*"',
                r'href="[^"]*\/p\d+[^"]*"',
                r'Pager-p p-\d+',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, html)
                if matches:
                    logger.info(f"✅ Pattern '{pattern}': {len(matches)} matches")
                    for match in matches[:3]:
                        logger.info(f"   - {match}")
                else:
                    logger.info(f"❌ Pattern '{pattern}': No matches")
                    
        except Exception as e:
            logger.error(f"Error checking HTML: {e}")
        
        # Try to find any links that might be pagination
        logger.info("\n🔍 Analyzing all links...")
        try:
            all_links = page.eles('a')
            pagination_candidates = []
            
            for link in all_links:
                try:
                    href = link.attr('href') or ''
                    text = link.text.strip()
                    classes = link.attr('class') or ''
                    
                    # Check if it looks like pagination
                    is_pagination = (
                        '/p' in href and re.search(r'/p\d+', href) or
                        'pager' in classes.lower() or
                        text.isdigit() and int(text) > 1 or
                        text in ['Next', '»', 'Last', 'Previous', '‹']
                    )
                    
                    if is_pagination:
                        pagination_candidates.append({
                            'text': text,
                            'href': href,
                            'classes': classes
                        })
                        
                except Exception as e:
                    continue
            
            if pagination_candidates:
                logger.info(f"✅ Found {len(pagination_candidates)} pagination candidates:")
                for i, candidate in enumerate(pagination_candidates[:5]):
                    logger.info(f"   {i+1}. '{candidate['text']}' -> {candidate['href']}")
                    logger.info(f"      Classes: {candidate['classes']}")
            else:
                logger.warning("❌ No pagination candidates found")
                
        except Exception as e:
            logger.error(f"Error analyzing links: {e}")
        
        # Check if this post actually has multiple pages
        logger.info("\n🔍 Checking if post has multiple pages...")
        try:
            # Look for comment count or other indicators
            comment_elements = page.eles('.Comment')
            logger.info(f"💬 Comments found: {len(comment_elements)}")
            
            # Check for "Load more" or similar
            load_more = page.eles('a:contains("Load")')
            if load_more:
                logger.info(f"🔄 Found {len(load_more)} 'Load' links")
                for link in load_more[:2]:
                    logger.info(f"   - '{link.text}' -> {link.attr('href')}")
            
            # Check for any indication of total comments
            meta_elements = page.eles('.MItem')
            for elem in meta_elements:
                text = elem.text.strip()
                if 'comment' in text.lower():
                    logger.info(f"📊 Meta info: {text}")
                    
        except Exception as e:
            logger.error(f"Error checking post details: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Diagnosis failed: {e}")
        return False
    finally:
        if page:
            try:
                page.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def main():
    """Main diagnosis function"""
    logger.info("🚀 Pagination Elements Diagnosis")
    logger.info("=" * 50)
    
    success = diagnose_pagination()
    
    if success:
        logger.info("\n✅ Diagnosis completed")
    else:
        logger.error("\n❌ Diagnosis failed")
    
    return success

if __name__ == "__main__":
    main()
