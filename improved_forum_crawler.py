"""
Improved Multi-threaded Forum Crawler for LowEndTalk Flash Sales
Author: AI Assistant
Date: 2025-07-14

This script provides a robust, secure, and maintainable solution for monitoring
LowEndTalk forums for flash sale announcements with comprehensive error handling,
logging, configuration management, and anti-detection measures.
"""

import asyncio
import json
import re
import time
import threading
import queue
import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
from concurrent.futures import ThreadPoolExecutor
import signal
import sys

from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings


@dataclass
class CrawlerConfig:
    """Configuration class for the forum crawler"""
    base_url: str = "https://lowendtalk.com/"
    monitor_url: str = "https://lowendtalk.com"
    state_file: str = "lowendtalk_crawl_state.json"
    results_file: str = "lowendtalk_flash_sales.json"
    
    # Flash sale detection keywords
    flash_sale_keywords: List[str] = field(default_factory=lambda: [
        "offer", "sale", "discount", "promo", "limited", "flash sale",
        "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
        "$/month", "$/yr", "$/year", "$/mo", "price", "deal", "special", "promotion"
    ])
    
    # Threading configuration
    num_workers: int = 5
    monitor_timeout: int = 3
    worker_timeout: int = 5
    queue_timeout: int = 1
    
    # Browser configuration
    headless: bool = False  # Changed to False for better Cloudflare bypass
    user_agents: List[str] = field(default_factory=lambda: [
        # Latest Chrome versions with realistic platform variations
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        # Latest Firefox versions
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
        # Edge versions
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        # Safari versions
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    ])
    
    # Timing configuration
    refresh_interval: int = 1
    scroll_delay: float = 2.0
    page_load_delay: float = 3.0
    
    # Rate limiting
    min_request_delay: float = 1.0
    max_request_delay: float = 3.0
    
    # Monitoring configuration
    max_posts_to_check: int = 10
    max_retries: int = 3

    # Pagination configuration
    enable_pagination: bool = True
    max_pages_to_check: int = 3
    comments_per_page: int = 25  # Typical comments per page on forums
    
    @classmethod
    def from_file(cls, config_path: str) -> 'CrawlerConfig':
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        except FileNotFoundError:
            logging.warning(f"Configuration file not found: {config_path}, using defaults")
            return cls()
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in configuration file: {e}")
            return cls()


class CrawlerLogger:
    """Enhanced logging system for the crawler"""
    
    @staticmethod
    def setup_logging(log_level: str = "INFO", log_file: str = "forum_crawler.log") -> logging.Logger:
        """Setup comprehensive logging configuration"""
        logger = logging.getLogger("forum_crawler")
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger


class AntiDetectionManager:
    """Manages anti-detection measures"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.last_request_time = 0
        self.request_count = 0
        
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.config.user_agents)
    
    def get_random_delay(self) -> float:
        """Get a random delay between requests"""
        return random.uniform(
            self.config.min_request_delay,
            self.config.max_request_delay
        )
    
    async def apply_rate_limiting(self) -> None:
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        min_delay = self.get_random_delay()
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def should_take_break(self) -> bool:
        """Determine if we should take a longer break"""
        # Take a break every 50 requests
        return self.request_count > 0 and self.request_count % 50 == 0
    
    async def take_break(self) -> None:
        """Take a longer break to avoid detection"""
        break_time = random.uniform(30, 120)  # 30-120 seconds
        logging.info(f"Taking anti-detection break for {break_time:.1f} seconds")
        await asyncio.sleep(break_time)


class StateManager:
    """Thread-safe state management"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.lock = threading.RLock()
        self.logger = logging.getLogger("forum_crawler.state")
    
    def load_state(self) -> Dict[str, Any]:
        """Load crawler state from file"""
        with self.lock:
            try:
                if Path(self.config.state_file).exists():
                    with open(self.config.state_file, 'r', encoding='utf-8') as f:
                        state = json.load(f)
                    self.logger.debug(f"Loaded state with {len(state.get('processed_posts', {}))} processed posts")
                    return state
                return {"processed_posts": {}, "last_run": None}
            except Exception as e:
                self.logger.error(f"Failed to load state file: {e}")
                return {"processed_posts": {}, "last_run": None}
    
    def save_state(self, state: Dict[str, Any]) -> None:
        """Save crawler state to file"""
        with self.lock:
            try:
                state["last_run"] = datetime.now(timezone.utc).isoformat()
                with open(self.config.state_file, 'w', encoding='utf-8') as f:
                    json.dump(state, f, ensure_ascii=False, indent=2)
                self.logger.debug("State saved successfully")
            except Exception as e:
                self.logger.error(f"Failed to save state file: {e}")
    
    def load_results(self) -> List[Dict[str, Any]]:
        """Load previous flash sale results"""
        with self.lock:
            try:
                if Path(self.config.results_file).exists():
                    with open(self.config.results_file, 'r', encoding='utf-8') as f:
                        results = json.load(f)
                    self.logger.debug(f"Loaded {len(results)} previous results")
                    return results
                return []
            except Exception as e:
                self.logger.error(f"Failed to load results file: {e}")
                return []
    
    def save_results(self, new_results: List[Dict[str, Any]]) -> None:
        """Save flash sale results with deduplication"""
        with self.lock:
            if not new_results:
                self.logger.info("No new results to save")
                return
            
            existing_results = self.load_results()
            
            # Create hash set for deduplication
            existing_hashes = {
                self._hash_result(result) for result in existing_results
            }
            
            new_items_added = 0
            for result in new_results:
                result_hash = self._hash_result(result)
                if result_hash not in existing_hashes:
                    existing_results.append(result)
                    existing_hashes.add(result_hash)
                    new_items_added += 1
            
            if new_items_added > 0:
                try:
                    with open(self.config.results_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_results, f, ensure_ascii=False, indent=2)
                    self.logger.info(f"Saved {new_items_added} new flash sale results")
                except Exception as e:
                    self.logger.error(f"Failed to save results: {e}")
            else:
                self.logger.info("No new unique results to save")
    
    def _hash_result(self, result: Dict[str, Any]) -> str:
        """Create a hash for result deduplication"""
        # Use post_url and comment_id for uniqueness
        key = f"{result.get('post_url', '')}-{result.get('comment_id', '')}"
        return hashlib.md5(key.encode()).hexdigest()


class FlashSaleDetector:
    """Enhanced flash sale detection with improved accuracy"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.detector")
        
        # Compile regex patterns for better performance
        self.price_patterns = [
            re.compile(r'\$\d+(?:\.\d{2})?(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'\d+(?:\.\d{2})?\s*(?:usd|eur|gbp)(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'(?:from|starting|only)\s*\$?\d+', re.IGNORECASE)
        ]
        
        self.spec_patterns = [
            re.compile(r'\d+\s*(?:gb|tb|mb)\s*(?:ram|memory|storage|disk)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:core|cpu|vcpu)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:gb|tb)\s*(?:bandwidth|traffic)', re.IGNORECASE)
        ]
    
    def is_flash_sale(self, text: str) -> Dict[str, Any]:
        """Enhanced flash sale detection with confidence scoring"""
        if not text or len(text.strip()) < 10:
            return {"is_flash_sale": False, "confidence": 0.0, "reasons": []}
        
        text_lower = text.lower()
        reasons = []
        confidence = 0.0
        
        # Check for flash sale keywords
        keyword_matches = 0
        for keyword in self.config.flash_sale_keywords:
            if keyword in text_lower:
                keyword_matches += 1
                reasons.append(f"keyword: {keyword}")
        
        if keyword_matches > 0:
            confidence += min(keyword_matches * 0.1, 0.5)
        
        # Check for price patterns
        price_matches = 0
        for pattern in self.price_patterns:
            if pattern.search(text):
                price_matches += 1
                reasons.append("price_pattern")
        
        if price_matches > 0:
            confidence += min(price_matches * 0.2, 0.4)
        
        # Check for specification patterns
        spec_matches = 0
        for pattern in self.spec_patterns:
            if pattern.search(text):
                spec_matches += 1
                reasons.append("spec_pattern")
        
        if spec_matches > 0:
            confidence += min(spec_matches * 0.1, 0.3)
        
        # Boost confidence for certain combinations
        if "flash" in text_lower and "sale" in text_lower:
            confidence += 0.3
            reasons.append("flash_sale_combo")
        
        if "limited" in text_lower and ("time" in text_lower or "offer" in text_lower):
            confidence += 0.2
            reasons.append("limited_time_offer")
        
        # Reduce confidence for certain patterns that might be false positives
        if "sold out" in text_lower or "expired" in text_lower:
            confidence *= 0.5
            reasons.append("negative_indicator")
        
        is_flash_sale = confidence >= 0.3  # Threshold for classification
        
        return {
            "is_flash_sale": is_flash_sale,
            "confidence": min(confidence, 1.0),
            "reasons": reasons,
            "keyword_matches": keyword_matches,
            "price_matches": price_matches,
            "spec_matches": spec_matches
        }


class TimeUtils:
    """Utility functions for time parsing and handling"""
    
    @staticmethod
    def parse_time_string(time_str: Optional[str]) -> Optional[datetime]:
        """Parse various time string formats to datetime objects"""
        if not time_str:
            return None
        
        try:
            # Handle ISO format
            dt_obj = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            if dt_obj.tzinfo is None:
                return dt_obj.replace(tzinfo=timezone.utc)
            return dt_obj
        except ValueError:
            pass
        
        # Handle relative formats like "July 13"
        current_year = datetime.now().year
        formats_to_try = [
            f"%B %d, {current_year}",
            f"%B %d {current_year}",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d"
        ]
        
        for fmt in formats_to_try:
            try:
                return datetime.strptime(time_str, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue
        
        logging.warning(f"Could not parse time string: {time_str}")
        return None


class BrowserManager:
    """Manages browser instances and tabs with proper resource cleanup"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.browser = None
        self.logger = logging.getLogger("forum_crawler.browser")
        self.anti_detection = AntiDetectionManager(config)
    
    def __enter__(self):
        """Context manager entry"""
        self.start_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.stop_browser()
    
    def start_browser(self) -> None:
        """Initialize browser with enhanced anti-detection settings for Cloudflare bypass"""
        try:
            # Configure DrissionPage settings
            Settings.set_singleton_tab_obj(False)

            # Create ChromiumOptions for better stealth
            from DrissionPage import ChromiumOptions

            options = ChromiumOptions()

            # Essential Cloudflare bypass arguments
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            # REMOVED: --disable-javascript (Cloudflare needs JavaScript!)
            # REMOVED: --disable-images (may help with detection)

            # Additional Cloudflare bypass arguments
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-component-extensions-with-background-pages')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-domain-reliability')
            options.add_argument('--disable-component-update')

            # Window and viewport settings to appear more human-like
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--start-maximized')

            # Set a realistic user agent
            user_agent = self.anti_detection.get_random_user_agent()
            options.add_argument(f'--user-agent={user_agent}')

            # Critical stealth settings for Cloudflare
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option('useAutomationExtension', False)

            # Additional prefs to avoid detection
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,  # Block notifications
                    "geolocation": 2,    # Block location requests
                },
                "profile.managed_default_content_settings": {
                    "images": 1  # Allow images (important for Cloudflare)
                }
            }
            options.add_experimental_option("prefs", prefs)

            # Create browser with options
            self.browser = Chromium(addr_or_opts=options)
            self.logger.info(f"Browser started with Cloudflare bypass configuration")
            self.logger.info(f"User agent: {user_agent[:50]}...")

        except Exception as e:
            self.logger.error(f"Failed to start browser: {e}")
            # Fallback to basic browser
            try:
                self.browser = Chromium()
                self.logger.info("Browser started with basic configuration")
            except Exception as fallback_error:
                self.logger.error(f"Fallback browser creation failed: {fallback_error}")
                raise
    
    def stop_browser(self) -> None:
        """Safely stop browser and cleanup resources"""
        if self.browser:
            try:
                self.browser.quit()
                self.logger.info("Browser stopped successfully")
            except Exception as e:
                self.logger.error(f"Error stopping browser: {e}")
    
    def create_tab(self) -> Optional[ChromiumPage]:
        """Create a new browser tab with error handling"""
        try:
            if not self.browser:
                raise RuntimeError("Browser not initialized")

            tab = self.browser.new_tab()

            # Apply anti-detection measures if the method exists
            try:
                if hasattr(tab, 'set') and hasattr(tab.set, 'user_agent'):
                    tab.set.user_agent(self.anti_detection.get_random_user_agent())
                elif hasattr(tab, 'set_user_agent'):
                    tab.set_user_agent(self.anti_detection.get_random_user_agent())
            except Exception as ua_error:
                self.logger.warning(f"Could not set user agent: {ua_error}")

            return tab
        except Exception as e:
            self.logger.error(f"Failed to create browser tab: {e}")
            return None

    def handle_cloudflare_verification(self, page: ChromiumPage, max_wait_time: int = 30) -> bool:
        """Handle Cloudflare verification if present"""
        try:
            self.logger.info("Checking for Cloudflare verification...")

            # Check for common Cloudflare indicators
            cloudflare_indicators = [
                'Checking your browser before accessing',
                'DDoS protection by Cloudflare',
                'Please wait while we verify',
                'cf-browser-verification',
                'cf-challenge-running',
                'Cloudflare'
            ]

            # Check page title and content for Cloudflare
            page_title = page.title if hasattr(page, 'title') else ""
            page_source = ""

            try:
                # Get page source safely
                page_source = page.html[:1000]  # First 1000 chars should be enough
            except Exception as e:
                self.logger.warning(f"Could not get page source: {e}")

            # Check if we're on a Cloudflare verification page
            is_cloudflare = any(indicator.lower() in page_title.lower() for indicator in cloudflare_indicators)
            is_cloudflare = is_cloudflare or any(indicator.lower() in page_source.lower() for indicator in cloudflare_indicators)

            if not is_cloudflare:
                self.logger.info("No Cloudflare verification detected")
                return True

            self.logger.info("Cloudflare verification detected, waiting for completion...")

            # Wait for verification to complete
            start_time = time.time()
            while time.time() - start_time < max_wait_time:
                try:
                    # Check if we've moved past the verification page
                    current_title = page.title if hasattr(page, 'title') else ""
                    current_url = page.url if hasattr(page, 'url') else ""

                    # If title changed and no longer contains Cloudflare indicators
                    if current_title != page_title:
                        is_still_cloudflare = any(indicator.lower() in current_title.lower() for indicator in cloudflare_indicators)
                        if not is_still_cloudflare:
                            self.logger.info("Cloudflare verification completed successfully")
                            return True

                    # Check if URL changed (successful redirect)
                    if 'lowendtalk.com' in current_url and 'cloudflare' not in current_url.lower():
                        self.logger.info("Successfully redirected past Cloudflare")
                        return True

                    # Wait a bit before checking again
                    time.sleep(2)

                except Exception as e:
                    self.logger.warning(f"Error during Cloudflare verification check: {e}")
                    time.sleep(2)

            self.logger.warning(f"Cloudflare verification timed out after {max_wait_time} seconds")
            return False

        except Exception as e:
            self.logger.error(f"Error handling Cloudflare verification: {e}")
            return False


class HealthMonitor:
    """Monitors the health of crawler components"""
    
    def __init__(self):
        self.thread_health = {}
        self.last_activity = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger("forum_crawler.health")
    
    def update_thread_health(self, thread_id: str, status: str, details: str = "") -> None:
        """Update thread health status"""
        with self.lock:
            self.thread_health[thread_id] = {
                "status": status,
                "details": details,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.last_activity[thread_id] = time.time()
    
    def check_thread_health(self, max_inactive_time: int = 300) -> Dict[str, Any]:
        """Check health of all threads"""
        with self.lock:
            current_time = time.time()
            health_report = {
                "healthy_threads": [],
                "unhealthy_threads": [],
                "inactive_threads": []
            }
            
            for thread_id, last_time in self.last_activity.items():
                time_since_activity = current_time - last_time
                thread_status = self.thread_health.get(thread_id, {})
                
                if time_since_activity > max_inactive_time:
                    health_report["inactive_threads"].append({
                        "thread_id": thread_id,
                        "inactive_time": time_since_activity,
                        "last_status": thread_status
                    })
                elif thread_status.get("status") == "error":
                    health_report["unhealthy_threads"].append({
                        "thread_id": thread_id,
                        "status": thread_status
                    })
                else:
                    health_report["healthy_threads"].append(thread_id)
            
            return health_report
    
    def log_health_summary(self) -> None:
        """Log a summary of system health"""
        health = self.check_thread_health()
        self.logger.info(f"Health Summary - Healthy: {len(health['healthy_threads'])}, "
                        f"Unhealthy: {len(health['unhealthy_threads'])}, "
                        f"Inactive: {len(health['inactive_threads'])}")


# Global instances for shared resources
health_monitor = HealthMonitor()
shutdown_event = threading.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logging.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_event.set()


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


class ForumCrawler:
    """Main crawler class orchestrating all components"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.main")
        self.state_manager = StateManager(config)
        self.detector = FlashSaleDetector(config)
        
        # Thread synchronization
        self.task_queue = queue.Queue()
        self.monitor_ready_event = threading.Event()
        self.all_flash_sales_found = []
        self.results_lock = threading.Lock()
        
        # Thread management
        self.worker_threads = []
        self.monitor_thread = None
        
    def start(self) -> None:
        """Start the crawler with all components"""
        self.logger.info("Starting Forum Crawler...")
        
        try:
            with BrowserManager(self.config) as browser_manager:
                self._start_threads(browser_manager)
                self._run_main_loop()
        except Exception as e:
            self.logger.error(f"Critical error in crawler: {e}")
        finally:
            self._cleanup()
    
    def _start_threads(self, browser_manager: BrowserManager) -> None:
        """Start monitor and worker threads"""
        # Start monitor thread
        self.monitor_thread = threading.Thread(
            target=self._monitor_thread_func,
            args=(browser_manager,),
            name="Monitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        # Start worker threads
        for i in range(self.config.num_workers):
            worker_thread = threading.Thread(
                target=self._worker_thread_func,
                args=(f"Worker-{i+1}", browser_manager),
                name=f"Worker-{i+1}",
                daemon=True
            )
            worker_thread.start()
            self.worker_threads.append(worker_thread)
        
        self.logger.info(f"Started {self.config.num_workers} worker threads and 1 monitor thread")
    
    def _run_main_loop(self) -> None:
        """Main loop for monitoring and health checks"""
        health_check_interval = 60  # Check health every minute
        last_health_check = 0
        
        while not shutdown_event.is_set():
            try:
                current_time = time.time()
                
                # Periodic health checks
                if current_time - last_health_check > health_check_interval:
                    health_monitor.log_health_summary()
                    last_health_check = current_time
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt")
                break
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                time.sleep(5)
    
    def _cleanup(self) -> None:
        """Cleanup resources and save final state"""
        self.logger.info("Starting cleanup process...")
        
        # Signal shutdown to all threads
        shutdown_event.set()
        
        # Wait for threads to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=10)
        
        # Save final results
        with self.results_lock:
            if self.all_flash_sales_found:
                self.state_manager.save_results(self.all_flash_sales_found)
                self.logger.info(f"Saved {len(self.all_flash_sales_found)} flash sale results")
        
        # Save final state
        final_state = self.state_manager.load_state()
        self.state_manager.save_state(final_state)
        
        self.logger.info("Cleanup completed")
    
    def _monitor_thread_func(self, browser_manager: BrowserManager) -> None:
        """Monitor thread function - tracks forum for updates"""
        thread_id = "monitor"
        self.browser_manager = browser_manager  # Store reference for Cloudflare handling
        health_monitor.update_thread_health(thread_id, "starting", "Initializing monitor thread")

        try:
            monitor_page = browser_manager.create_tab()
            if not monitor_page:
                raise RuntimeError("Failed to create monitor page")

            # Perform initial scan with timeout protection
            try:
                self._perform_initial_scan(monitor_page, thread_id)
                self.monitor_ready_event.set()
                self.logger.info("Monitor thread ready event set - workers can now start")
            except Exception as e:
                self.logger.error(f"Initial scan failed: {e}")
                # Set the event anyway to prevent workers from hanging
                self.monitor_ready_event.set()
                raise

            # Continuous monitoring loop
            while not shutdown_event.is_set():
                try:
                    self._perform_monitoring_cycle(monitor_page, thread_id)
                    time.sleep(self.config.refresh_interval)
                except Exception as e:
                    self.logger.error(f"Error in monitoring cycle: {e}")
                    health_monitor.update_thread_health(thread_id, "error", str(e))
                    time.sleep(30)  # Wait before retrying

        except Exception as e:
            self.logger.error(f"Critical error in monitor thread: {e}")
            health_monitor.update_thread_health(thread_id, "critical_error", str(e))
            # Always set the ready event to prevent workers from hanging
            self.monitor_ready_event.set()
        finally:
            health_monitor.update_thread_health(thread_id, "stopped", "Monitor thread ended")
            self.logger.info("Monitor thread ended")
    
    def _perform_initial_scan(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform initial scan of the forum"""
        health_monitor.update_thread_health(thread_id, "initial_scan", "Loading forum page")

        try:
            self.logger.info(f"Loading forum page: {self.config.monitor_url}")
            monitor_page.get(self.config.monitor_url)

            # Wait for page to load
            self.logger.info("Waiting for page to load...")
            time.sleep(self.config.page_load_delay)

            # Handle Cloudflare verification if present
            if not self.browser_manager.handle_cloudflare_verification(monitor_page, max_wait_time=60):
                raise RuntimeError("Failed to pass Cloudflare verification")

            # Additional wait after Cloudflare verification
            time.sleep(3)

            # Try multiple selectors to find posts container
            posts_container = None
            selectors_to_try = [
                'tag:ul@class=DataList Discussions',
                '.DataList.Discussions',
                'ul.DataList.Discussions',
                '.ItemDiscussion'  # Try to find individual posts if container not found
            ]

            for selector in selectors_to_try:
                self.logger.info(f"Trying selector: {selector}")
                try:
                    posts_container = monitor_page.ele(selector, timeout=10)
                    if posts_container:
                        self.logger.info(f"Found posts container with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not posts_container:
                # Log page content for debugging
                self.logger.error("Could not find posts container. Page title: " +
                                (monitor_page.title if hasattr(monitor_page, 'title') else "Unknown"))

                # Try a more basic approach - just look for any posts
                try:
                    # Check if we can find any discussion items at all
                    any_posts = monitor_page.eles('.ItemDiscussion')
                    if any_posts:
                        self.logger.info(f"Found {len(any_posts)} individual posts, proceeding without container")
                        # Create a mock container for processing
                        posts_container = monitor_page  # Use the page itself as container
                    else:
                        raise RuntimeError("No posts found on page - site may be down or structure changed")
                except Exception as e:
                    self.logger.error(f"Failed to find any posts: {e}")
                    raise RuntimeError("Could not find any posts on the forum page")

            health_monitor.update_thread_health(thread_id, "scanning", "Processing initial posts")
            self._process_posts(posts_container, thread_id, is_initial=True)

            health_monitor.update_thread_health(thread_id, "ready", "Initial scan completed")
            self.logger.info("Initial scan completed, monitor thread ready")

        except Exception as e:
            self.logger.error(f"Error in initial scan: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Initial scan failed: {e}")
            raise
    
    def _perform_monitoring_cycle(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform a single monitoring cycle"""
        health_monitor.update_thread_health(thread_id, "refreshing", "Refreshing forum page")
        
        monitor_page.refresh()
        time.sleep(self.config.page_load_delay)
        
        posts_container = monitor_page.ele('tag:ul@class=DataList Discussions', timeout=30)
        if not posts_container:
            self.logger.warning("Could not find posts container after refresh")
            return
        
        health_monitor.update_thread_health(thread_id, "processing", "Processing updated posts")
        self._process_posts(posts_container, thread_id, is_initial=False)
        
        health_monitor.update_thread_health(thread_id, "active", "Monitoring cycle completed")
    
    def _process_posts(self, posts_container, thread_id: str, is_initial: bool = False) -> None:
        """Process posts from the container"""
        try:
            # Get all discussion items - try multiple approaches
            all_items = []

            # Try different selectors to find discussion items
            # Based on debug analysis: actual classes are "Item Unread ItemDiscussion ItemDiscussion-withPhoto"
            selectors_to_try = [
                'xpath:./li[contains(@class, "Item")]',  # Direct children with Item class
                'li.Item',  # Li elements with Item class
                'xpath:.//li[contains(@class, "ItemDiscussion")]',  # Fallback to original
                '.Item'  # Any element with Item class
            ]

            for selector in selectors_to_try:
                try:
                    items = posts_container.eles(selector)
                    if items:
                        all_items = items
                        self.logger.info(f"Found {len(all_items)} items using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not all_items:
                self.logger.warning("No discussion items found, trying fallback approach")
                # Fallback: try to find any items that might be posts
                try:
                    all_items = posts_container.eles('li')
                    self.logger.info(f"Fallback found {len(all_items)} li elements")
                except Exception as e:
                    self.logger.error(f"Fallback approach failed: {e}")
                    return

            # Filter out announcements - use faster approach
            non_announcement_posts = []
            for i, item in enumerate(all_items):
                try:
                    # Use a faster method - check if class contains announcement
                    item_classes = item.attr('class') or ''
                    if 'announcement' not in item_classes.lower():
                        non_announcement_posts.append(item)
                        if i < 5:  # Log first few for debugging
                            self.logger.debug(f"Item {i+1} classes: {item_classes}")
                    else:
                        self.logger.debug(f"Skipping announcement item {i+1}")
                except Exception as e:
                    # If we can't check for announcements, include the item anyway
                    self.logger.warning(f"Could not check announcement status for item {i+1}: {e}")
                    non_announcement_posts.append(item)

            # Limit posts to check
            posts_to_check = non_announcement_posts[:self.config.max_posts_to_check]

            scan_type = "Initial" if is_initial else "Update"
            self.logger.info(f"{scan_type} scan found {len(posts_to_check)} posts to check")

            if not posts_to_check:
                self.logger.warning("No posts to check after filtering")
                return

            # Load current state
            self.logger.info("Loading current state...")
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            self.logger.info(f"Loaded state with {len(processed_posts)} previously processed posts")

            for i, item in enumerate(posts_to_check):
                try:
                    self.logger.info(f"Processing post {i+1}/{len(posts_to_check)}")
                    post_data = self._extract_post_data(item)
                    if not post_data:
                        self.logger.warning(f"Could not extract data from post {i+1}")
                        continue

                    safe_title = post_data['post_title'][:50].encode('ascii', 'ignore').decode('ascii')
                    self.logger.info(f"Extracted post: {safe_title}...")
                    should_queue = self._should_process_post(post_data, processed_posts)

                    if should_queue:
                        self.task_queue.put(post_data)
                        self.logger.info(f"Queued post: {safe_title}...")
                    else:
                        # Update state for unchanged posts
                        self._update_post_state(post_data, processed_posts)
                        self.logger.debug(f"Skipped unchanged post: {post_data['post_title'][:50]}...")

                except Exception as e:
                    self.logger.error(f"Error processing post item {i+1}: {e}")
                    continue

            # Save updated state
            self.logger.info("Saving updated state...")
            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)
            self.logger.info(f"Post processing completed for {scan_type} scan")

        except Exception as e:
            self.logger.error(f"Error in _process_posts: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Post processing error: {e}")
            raise  # Re-raise to ensure the error is handled properly

    def _extract_post_data(self, item) -> Optional[Dict[str, Any]]:
        """Extract post data from a forum item"""
        try:
            # Use the working approach from quick_fix_crawler.py
            # Try to find title link directly (this works!)
            title_link = item.ele('a', timeout=2)
            if not title_link:
                self.logger.debug("No title link found")
                return None

            post_title = title_link.text.strip()
            post_url = title_link.link

            if not post_title or not post_url:
                self.logger.debug("Missing title or URL")
                return None

            # Ensure absolute URL
            if not post_url.startswith('http'):
                post_url = self.config.base_url.rstrip('/') + post_url

            # Extract comment count using the working method
            current_comment_count = 0
            try:
                # Look for comment count in various places
                count_elements = item.eles('@@class:Number@@title:comments')
                for elem in count_elements:
                    text = elem.text.strip()
                    if text.isdigit():
                        current_comment_count = int(text)
                        break
                    elif 'k' in text.lower():
                        try:
                            num = float(text.lower().replace('k', ''))
                            current_comment_count = int(num * 1000)
                            break
                        except:
                            pass
            except Exception as e:
                self.logger.debug(f"Could not extract comment count: {e}")

            # Extract last comment date - simplified approach
            datetime_attr = None
            try:
                time_elements = item.eles('tag:time')
                if time_elements:
                    datetime_attr = time_elements[0].attr('datetime')
            except Exception as e:
                self.logger.debug(f"Could not extract datetime: {e}")

            # If we got here, we have at least title and URL
            # Use safe logging to avoid Unicode errors
            safe_title = post_title[:50].encode('ascii', 'ignore').decode('ascii')
            self.logger.info(f"Extracted: {safe_title}... (Comments: {current_comment_count})")

            return {
                "post_url": post_url,
                "post_title": post_title,
                "current_comment_count": current_comment_count,
                "datetime_attr": datetime_attr or datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error extracting post data: {e}")
            return None

    def _should_process_post(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> bool:
        """Determine if a post should be processed"""
        post_url = post_data["post_url"]
        current_comment_count = post_data["current_comment_count"]
        datetime_attr = post_data["datetime_attr"]

        if post_url not in processed_posts:
            self.logger.debug(f"New post detected: {post_data['post_title'][:50]}...")
            return True

        prev_state = processed_posts[post_url]
        prev_comment_count = prev_state.get("last_comment_count", 0)
        prev_datetime_str = prev_state.get("last_comment_datetime")

        # Check comment count increase
        if current_comment_count > prev_comment_count:
            self.logger.debug(f"Comment count increased: {prev_comment_count} -> {current_comment_count}")
            return True

        # Check timestamp update
        if datetime_attr and prev_datetime_str:
            current_datetime = TimeUtils.parse_time_string(datetime_attr)
            prev_datetime = TimeUtils.parse_time_string(prev_datetime_str)

            if current_datetime and prev_datetime and current_datetime > prev_datetime:
                self.logger.debug(f"Timestamp updated: {prev_datetime_str} -> {datetime_attr}")
                return True

        return False

    def _update_post_state(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> None:
        """Update state for posts that don't need processing"""
        post_url = post_data["post_url"]
        prev_state = processed_posts.get(post_url, {})

        processed_posts[post_url] = {
            "last_comment_count": post_data["current_comment_count"],
            "last_comment_datetime": post_data["datetime_attr"],
            "processed_comment_ids": prev_state.get("processed_comment_ids", [])
        }

    def _worker_thread_func(self, worker_id: str, browser_manager: BrowserManager) -> None:
        """Worker thread function - processes posts from the queue"""
        health_monitor.update_thread_health(worker_id, "starting", "Initializing worker thread")

        try:
            worker_page = browser_manager.create_tab()
            if not worker_page:
                raise RuntimeError("Failed to create worker page")

            # Wait for monitor to be ready
            self.logger.info(f"[{worker_id}] Waiting for monitor thread...")
            if not self.monitor_ready_event.wait(timeout=self.config.monitor_timeout):
                self.logger.warning(f"[{worker_id}] Monitor thread not ready, worker exiting")
                return

            health_monitor.update_thread_health(worker_id, "ready", "Worker ready to process tasks")

            # Main processing loop
            while not shutdown_event.is_set():
                try:
                    # Get task from queue
                    task_data = self.task_queue.get(timeout=self.config.queue_timeout)

                    health_monitor.update_thread_health(worker_id, "processing", f"Processing: {task_data['post_title'][:30]}...")

                    # Process the post
                    self._process_post_comments(worker_id, worker_page, task_data)

                    self.task_queue.task_done()
                    health_monitor.update_thread_health(worker_id, "active", "Task completed")

                except queue.Empty:
                    # No tasks available, continue waiting
                    health_monitor.update_thread_health(worker_id, "waiting", "Waiting for tasks")
                    continue
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing task: {e}")
                    health_monitor.update_thread_health(worker_id, "error", str(e))

                    # Try to recover by recreating the page
                    try:
                        worker_page.close()
                        worker_page = browser_manager.create_tab()
                    except Exception as recovery_error:
                        self.logger.error(f"[{worker_id}] Failed to recover: {recovery_error}")
                        break

                    self.task_queue.task_done()

        except Exception as e:
            self.logger.error(f"[{worker_id}] Critical error in worker thread: {e}")
            health_monitor.update_thread_health(worker_id, "critical_error", str(e))
        finally:
            health_monitor.update_thread_health(worker_id, "stopped", "Worker thread ended")
            self.logger.info(f"[{worker_id}] Worker thread ended")

    def _process_post_comments(self, worker_id: str, worker_page: ChromiumPage, task_data: Dict[str, Any]) -> None:
        """Process comments in a post for flash sales"""
        post_url = task_data["post_url"]
        post_title = task_data["post_title"]
        current_comment_count = task_data["current_comment_count"]
        datetime_attr = task_data["datetime_attr"]

        safe_title = post_title.encode('ascii', 'ignore').decode('ascii')
        self.logger.info(f"[{worker_id}] Processing: {safe_title}")

        try:
            # Navigate to post
            worker_page.get(post_url)
            worker_page.wait.load_start()

            # Handle Cloudflare verification if present
            if hasattr(self, 'browser_manager'):
                if not self.browser_manager.handle_cloudflare_verification(worker_page, max_wait_time=30):
                    self.logger.warning(f"[{worker_id}] Cloudflare verification failed for {post_url}")
                    return

            # Apply anti-detection delay
            time.sleep(random.uniform(1, 3))

            # Try to navigate to the last page to get newest comments
            # This will handle both paginated and non-paginated posts
            all_comments = self._get_all_comments_with_pagination(worker_page, worker_id, current_comment_count)
            self.logger.info(f"[{worker_id}] Found {len(all_comments)} total comments")

            # Get previously processed comment IDs
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            prev_processed_ids = set(processed_posts.get(post_url, {}).get("processed_comment_ids", []))

            new_processed_ids = set()
            flash_sales_found = []

            # Process each comment
            for comment_element in all_comments:
                try:
                    comment_id = comment_element.attr('id')
                    if not comment_id or comment_id in prev_processed_ids:
                        continue

                    comment_text_element = comment_element.ele('.Message')
                    if not comment_text_element:
                        continue

                    comment_text = comment_text_element.text.strip()
                    if len(comment_text) < 10:  # Skip very short comments
                        continue

                    # Check for flash sale
                    detection_result = self.detector.is_flash_sale(comment_text)

                    if detection_result["is_flash_sale"]:
                        flash_sale_info = {
                            "post_title": post_title,
                            "post_url": post_url,
                            "comment_id": comment_id,
                            "comment_text": comment_text,
                            "confidence": detection_result["confidence"],
                            "detection_reasons": detection_result["reasons"],
                            "crawled_time": datetime.now(timezone.utc).isoformat(),
                            "worker_id": worker_id
                        }

                        flash_sales_found.append(flash_sale_info)

                        self.logger.info(f"[{worker_id}] 🎉 Flash sale detected! "
                                       f"Confidence: {detection_result['confidence']:.2f}, "
                                       f"Comment ID: {comment_id}")
                        self.logger.debug(f"[{worker_id}] Content: {comment_text[:100]}...")

                    new_processed_ids.add(comment_id)

                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing comment: {e}")
                    continue

            # Update results and state
            if flash_sales_found:
                with self.results_lock:
                    self.all_flash_sales_found.extend(flash_sales_found)

                self.logger.info(f"[{worker_id}] Found {len(flash_sales_found)} flash sales in this post")

            # Update post state
            self._update_processed_post_state(post_url, current_comment_count, datetime_attr,
                                            prev_processed_ids.union(new_processed_ids))

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error processing post comments: {e}")
            raise

    def _get_all_comments_with_pagination(self, page: ChromiumPage, worker_id: str, expected_comment_count: int) -> List:
        """Get all comments, handling both paginated and non-paginated posts"""
        all_comments = []

        try:
            self.logger.info(f"[{worker_id}] 🔍 Checking for pagination...")

            # First, try to navigate to the last page if pagination exists
            last_page_reached = self._navigate_to_last_page(page, worker_id, expected_comment_count)

            if last_page_reached:
                self.logger.info(f"[{worker_id}] ✅ Successfully navigated to last page")
            else:
                self.logger.info(f"[{worker_id}] ℹ️ No pagination found or navigation failed - using current page")

            # Get comments from current page (either the last page or the only page)
            current_page_comments = self._get_comments_from_current_page(page, worker_id)
            all_comments.extend(current_page_comments)

            self.logger.info(f"[{worker_id}] 💬 Got {len(current_page_comments)} comments from current page")

            # If we successfully reached a last page and there might be more comments, work backwards
            if last_page_reached and len(all_comments) < expected_comment_count:
                self.logger.info(f"[{worker_id}] 🔄 Looking for additional comments on previous pages...")
                additional_comments = self._get_comments_from_previous_pages(
                    page, worker_id,
                    min(expected_comment_count - len(all_comments), self.config.max_pages_to_check * self.config.comments_per_page)
                )
                all_comments.extend(additional_comments)
                self.logger.info(f"[{worker_id}] 💬 Got {len(additional_comments)} additional comments from previous pages")

            # If no comments found, try fallback method
            if not all_comments:
                self.logger.warning(f"[{worker_id}] No comments found, trying fallback method...")
                all_comments = self._get_comments_fallback(page, worker_id)

            self.logger.info(f"[{worker_id}] Total comments collected: {len(all_comments)}")
            return all_comments

        except Exception as e:
            self.logger.error(f"[{worker_id}] ❌ Error in pagination logic: {e}")
            # Always fallback to simple method
            return self._get_comments_fallback(page, worker_id)

    def _navigate_to_last_page(self, page: ChromiumPage, worker_id: str, expected_comment_count: int) -> bool:
        """Navigate to the last page of comments. Returns True if successfully navigated to a different page."""
        try:
            original_url = page.url
            self.logger.info(f"[{worker_id}] 🔍 Looking for pagination on: {original_url}")

            # Wait a moment for page to fully load
            time.sleep(2)

            # Based on actual LowEndTalk structure analysis:
            # URLs: /discussion/ID/title/p2, /p3, etc.
            # Classes: Pager-p p-2, Pager-p p-3, etc.

            # First, try to find pagination using the most reliable method
            max_page = 1
            last_page_link = None
            pagination_found = False

            # Method 1: Look for numbered pagination links in the Pager
            try:
                pager_links = page.eles('.Pager a')
                if pager_links:
                    self.logger.info(f"[{worker_id}] ✅ Found {len(pager_links)} pager links")
                    pagination_found = True

                    for link in pager_links:
                        try:
                            link_text = link.text.strip()
                            link_href = link.attr('href') or ''

                            # Skip non-numeric links like "Previous", "Next"
                            if link_text.isdigit():
                                page_num = int(link_text)
                                if page_num > max_page:
                                    max_page = page_num
                                    last_page_link = link
                                    self.logger.info(f"[{worker_id}] 📈 Found page {page_num} link")

                            # Also check href for page numbers
                            import re
                            page_match = re.search(r'/p(\d+)/?(?:\?|$|#)', link_href)
                            if page_match:
                                page_num = int(page_match.group(1))
                                if page_num > max_page:
                                    max_page = page_num
                                    last_page_link = link
                                    self.logger.info(f"[{worker_id}] 📈 Found page {page_num} from href")

                        except Exception as e:
                            self.logger.debug(f"[{worker_id}] Error analyzing pager link: {e}")
                            continue

            except Exception as e:
                self.logger.debug(f"[{worker_id}] Error finding pager links: {e}")

            # Method 2: If no pager found, try direct href search
            if not pagination_found:
                try:
                    all_links = page.eles('a[href*="/p"]')
                    if all_links:
                        self.logger.info(f"[{worker_id}] ✅ Found {len(all_links)} /p links")
                        pagination_found = True

                        for link in all_links:
                            try:
                                link_href = link.attr('href') or ''

                                # Only consider links from the same discussion
                                if original_url.split('/p')[0] in link_href:
                                    import re
                                    page_match = re.search(r'/p(\d+)', link_href)
                                    if page_match:
                                        page_num = int(page_match.group(1))
                                        if page_num > max_page:
                                            max_page = page_num
                                            last_page_link = link
                                            self.logger.info(f"[{worker_id}] 📈 Found page {page_num} from direct search")

                            except Exception as e:
                                self.logger.debug(f"[{worker_id}] Error analyzing direct link: {e}")
                                continue

                except Exception as e:
                    self.logger.debug(f"[{worker_id}] Error in direct href search: {e}")

            # If we found pagination, try to navigate to the last page
            if pagination_found and last_page_link and max_page > 1:
                self.logger.info(f"[{worker_id}] 🚀 Navigating to page {max_page} (highest found)")

                try:
                    original_url = page.url

                    # Click the link to the last page
                    last_page_link.click()

                    # Wait for navigation and page load
                    time.sleep(3)

                    # Verify navigation was successful
                    new_url = page.url
                    if new_url != original_url:
                        self.logger.info(f"[{worker_id}] ✅ Successfully navigated to: {new_url}")

                        # Additional verification: check if we're on the expected page
                        if f"/p{max_page}" in new_url or f"page={max_page}" in new_url:
                            self.logger.info(f"[{worker_id}] ✅ Confirmed on page {max_page}")
                        else:
                            self.logger.warning(f"[{worker_id}] ⚠️ URL doesn't contain expected page number")

                        return True
                    else:
                        self.logger.warning(f"[{worker_id}] ⚠️ URL didn't change after clicking pagination link")

                except Exception as click_error:
                    self.logger.error(f"[{worker_id}] ❌ Failed to click pagination link: {click_error}")

            # Alternative method: Try direct URL navigation if click failed
            elif pagination_found and max_page > 1:
                self.logger.info(f"[{worker_id}] 🔄 Trying direct URL navigation to page {max_page}")

                try:
                    # Construct the URL for the last page
                    base_url = original_url.split('/p')[0]  # Remove any existing page number
                    last_page_url = f"{base_url}/p{max_page}"

                    self.logger.info(f"[{worker_id}] 🌐 Navigating to: {last_page_url}")
                    page.get(last_page_url)

                    time.sleep(3)

                    new_url = page.url
                    if f"/p{max_page}" in new_url:
                        self.logger.info(f"[{worker_id}] ✅ Direct navigation successful!")

                        # Count comments
                        new_comments = page.eles('.Comment')
                        self.logger.info(f"[{worker_id}] 💬 Comments on page {max_page}: {len(new_comments)}")

                        return True
                    else:
                        self.logger.warning(f"[{worker_id}] ⚠️ Direct navigation failed")

                except Exception as direct_error:
                    self.logger.error(f"[{worker_id}] ❌ Direct navigation failed: {direct_error}")

            # Alternative approach: look for "Last", "»", or "Next" links
            if not pagination_found or max_page <= 1:
                self.logger.info(f"[{worker_id}] 🔍 Trying alternative pagination methods...")

                last_link_selectors = [
                    ('Last page link', 'a[title*="Last"]'),
                    ('Last page text', 'a:contains("Last")'),
                    ('Next arrow', 'a:contains("»")'),
                    ('Next page', 'a[title*="Next"]'),
                    ('Right arrow', 'a:contains(">")')
                ]

                original_url = page.url

                for selector_name, selector in last_link_selectors:
                    try:
                        links = page.eles(selector)
                        if links:
                            link = links[0]  # Take the first match
                            link_text = link.text.strip()
                            link_href = link.attr('href') or ''

                            self.logger.info(f"[{worker_id}] 🔗 Found {selector_name}: '{link_text}' -> {link_href}")

                            # Click the link
                            link.click()
                            time.sleep(3)

                            new_url = page.url
                            if new_url != original_url:
                                self.logger.info(f"[{worker_id}] ✅ Successfully navigated using {selector_name}")
                                return True
                            else:
                                self.logger.warning(f"[{worker_id}] ⚠️ {selector_name} didn't change URL")

                    except Exception as e:
                        self.logger.debug(f"[{worker_id}] {selector_name} failed: {e}")
                        continue

            # If we get here, no pagination was found or navigation failed
            if not pagination_found:
                self.logger.info(f"[{worker_id}] ℹ️ No pagination detected - post has single page")
                # This is not an error - many posts have only one page
                return False
            else:
                self.logger.warning(f"[{worker_id}] ⚠️ Pagination detected but navigation failed")
                # Still not a critical error - we can work with the current page
                return False

        except Exception as e:
            self.logger.error(f"[{worker_id}] ❌ Error in pagination navigation: {e}")
            return False

    def _get_comments_from_current_page(self, page: ChromiumPage, worker_id: str) -> List:
        """Get all comments from the current page"""
        try:
            # Scroll to load any lazy-loaded content
            self._scroll_to_load_comments(page, worker_id)

            # Get comments using multiple selectors
            comment_selectors = [
                '.Comment',
                '.CommentWrap',
                '.ItemComment',
                '[class*="Comment"]'
            ]

            comments = []
            for selector in comment_selectors:
                try:
                    found_comments = page.eles(selector)
                    if found_comments:
                        comments = found_comments
                        self.logger.debug(f"[{worker_id}] Found {len(comments)} comments using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"[{worker_id}] Comment selector {selector} failed: {e}")
                    continue

            return comments

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error getting comments from current page: {e}")
            return []

    def _get_comments_from_previous_pages(self, page: ChromiumPage, worker_id: str, needed_count: int) -> List:
        """Get comments from previous pages if needed"""
        additional_comments = []

        try:
            # Look for "Previous" or "‹" links to go back
            prev_selectors = [
                'a[title*="Previous"]',
                'a[title*="previous"]',
                'a:contains("Previous")',
                'a:contains("‹")',
                'a:contains("<")'
            ]

            pages_checked = 0
            max_pages_to_check = 3  # Limit to avoid infinite loops

            while len(additional_comments) < needed_count and pages_checked < max_pages_to_check:
                prev_link = None

                for selector in prev_selectors:
                    try:
                        prev_link = page.ele(selector)
                        if prev_link:
                            break
                    except:
                        continue

                if not prev_link:
                    self.logger.debug(f"[{worker_id}] No previous page link found")
                    break

                # Click previous page
                prev_link.click()
                time.sleep(3)
                pages_checked += 1

                # Get comments from this page
                page_comments = self._get_comments_from_current_page(page, worker_id)
                additional_comments.extend(page_comments)

                self.logger.debug(f"[{worker_id}] Got {len(page_comments)} comments from previous page {pages_checked}")

            return additional_comments

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error getting comments from previous pages: {e}")
            return additional_comments

    def _scroll_to_load_comments(self, page: ChromiumPage, worker_id: str) -> None:
        """Scroll page to load all comments on current page"""
        try:
            last_height = page.scroll.to_bottom()
            scroll_attempts = 0
            max_scroll_attempts = 5  # Reduced since we're now handling pagination

            while scroll_attempts < max_scroll_attempts:
                time.sleep(self.config.scroll_delay)
                new_height = page.scroll.to_bottom()

                if new_height == last_height:
                    break  # No more content to load

                last_height = new_height
                scroll_attempts += 1

                self.logger.debug(f"[{worker_id}] Scrolled to load more comments (attempt {scroll_attempts})")

            if scroll_attempts >= max_scroll_attempts:
                self.logger.debug(f"[{worker_id}] Reached maximum scroll attempts")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error during scrolling: {e}")

    def _get_comments_fallback(self, page: ChromiumPage, worker_id: str) -> List:
        """Fallback method to get comments if pagination fails"""
        try:
            self.logger.info(f"[{worker_id}] Using fallback method to get comments")
            self._scroll_to_load_comments(page, worker_id)
            comments = page.eles('.Comment')
            return comments
        except Exception as e:
            self.logger.error(f"[{worker_id}] Fallback method also failed: {e}")
            return []

    def _update_processed_post_state(self, post_url: str, comment_count: int,
                                   datetime_attr: str, processed_ids: Set[str]) -> None:
        """Update the processed state for a post"""
        try:
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})

            processed_posts[post_url] = {
                "last_comment_count": comment_count,
                "last_comment_datetime": datetime_attr,
                "processed_comment_ids": list(processed_ids)
            }

            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)

        except Exception as e:
            self.logger.error(f"Error updating post state: {e}")


def main():
    """Main function to run the improved forum crawler"""
    # Setup logging
    logger = CrawlerLogger.setup_logging("INFO", "forum_crawler.log")

    try:
        # Load configuration
        config = CrawlerConfig.from_file("crawler_config.json")

        logger.info("Starting Improved Forum Crawler")
        logger.info(f"Configuration: {config.num_workers} workers, "
                   f"{config.refresh_interval}s refresh interval")

        # Create and start crawler
        crawler = ForumCrawler(config)
        crawler.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Critical error in main: {e}")
    finally:
        logger.info("Forum crawler shutdown complete")


if __name__ == "__main__":
    main()
