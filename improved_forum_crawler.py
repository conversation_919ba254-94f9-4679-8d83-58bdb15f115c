"""
Improved Multi-threaded Forum Crawler for LowEndTalk Flash Sales
Author: AI Assistant
Date: 2025-07-14

This script provides a robust, secure, and maintainable solution for monitoring
LowEndTalk forums for flash sale announcements with comprehensive error handling,
logging, configuration management, and anti-detection measures.
"""

import asyncio
import json
import re
import time
import threading
import queue
import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
from concurrent.futures import ThreadPoolExecutor
import signal
import sys

from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings


@dataclass
class CrawlerConfig:
    """Configuration class for the forum crawler"""
    base_url: str = "https://lowendtalk.com/"
    monitor_url: str = "https://lowendtalk.com"
    state_file: str = "lowendtalk_crawl_state.json"
    results_file: str = "lowendtalk_flash_sales.json"
    
    # Flash sale detection keywords
    flash_sale_keywords: List[str] = field(default_factory=lambda: [
        "offer", "sale", "discount", "promo", "limited", "flash sale",
        "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
        "$/month", "$/yr", "$/year", "$/mo", "price", "deal", "special", "promotion"
    ])
    
    # Threading configuration
    num_workers: int = 5
    monitor_timeout: int = 30  # Increased from 3 to 30 seconds
    worker_timeout: int = 5
    queue_timeout: int = 1
    
    # Browser configuration
    headless: bool = False  # Changed to False for better Cloudflare bypass
    user_agents: List[str] = field(default_factory=lambda: [
        # Latest Chrome versions with realistic platform variations
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        # Latest Firefox versions
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
        # Edge versions
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        # Safari versions
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    ])
    
    # Timing configuration
    refresh_interval: int = 1
    scroll_delay: float = 2.0
    page_load_delay: float = 3.0
    
    # Rate limiting
    min_request_delay: float = 1.0
    max_request_delay: float = 3.0
    
    # Monitoring configuration
    max_posts_to_check: int = 10
    max_retries: int = 3

    # Pagination configuration
    enable_pagination: bool = True
    max_pages_to_check: int = 3
    comments_per_page: int = 25  # Typical comments per page on forums

    # New post processing configuration
    min_comments_for_new_posts: int = 5  # Skip new posts with fewer comments

    # Real-time monitoring configuration
    monitor_interval: int = 45  # Seconds between monitoring scans
    
    @classmethod
    def from_file(cls, config_path: str) -> 'CrawlerConfig':
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        except FileNotFoundError:
            logging.warning(f"Configuration file not found: {config_path}, using defaults")
            return cls()
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in configuration file: {e}")
            return cls()


class CrawlerLogger:
    """Enhanced logging system for the crawler"""
    
    @staticmethod
    def setup_logging(log_level: str = "INFO", log_file: str = "forum_crawler.log") -> logging.Logger:
        """Setup comprehensive logging configuration"""
        logger = logging.getLogger("forum_crawler")
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger


class AntiDetectionManager:
    """Manages anti-detection measures"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.last_request_time = 0
        self.request_count = 0
        
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.config.user_agents)
    
    def get_random_delay(self) -> float:
        """Get a random delay between requests"""
        return random.uniform(
            self.config.min_request_delay,
            self.config.max_request_delay
        )
    
    async def apply_rate_limiting(self) -> None:
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        min_delay = self.get_random_delay()
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def should_take_break(self) -> bool:
        """Determine if we should take a longer break"""
        # Take a break every 50 requests
        return self.request_count > 0 and self.request_count % 50 == 0
    
    async def take_break(self) -> None:
        """Take a longer break to avoid detection"""
        break_time = random.uniform(30, 120)  # 30-120 seconds
        logging.info(f"Taking anti-detection break for {break_time:.1f} seconds")
        await asyncio.sleep(break_time)


class StateManager:
    """Thread-safe state management"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.lock = threading.RLock()
        self.logger = logging.getLogger("forum_crawler.state")
    
    def load_state(self) -> Dict[str, Any]:
        """Load crawler state from file"""
        with self.lock:
            try:
                if Path(self.config.state_file).exists():
                    with open(self.config.state_file, 'r', encoding='utf-8') as f:
                        state = json.load(f)
                    self.logger.debug(f"Loaded state with {len(state.get('processed_posts', {}))} processed posts")
                    return state
                return {"processed_posts": {}, "last_run": None}
            except Exception as e:
                self.logger.error(f"Failed to load state file: {e}")
                return {"processed_posts": {}, "last_run": None}
    
    def save_state(self, state: Dict[str, Any]) -> None:
        """Save crawler state to file"""
        with self.lock:
            try:
                state["last_run"] = datetime.now(timezone.utc).isoformat()
                with open(self.config.state_file, 'w', encoding='utf-8') as f:
                    json.dump(state, f, ensure_ascii=False, indent=2)
                self.logger.debug("State saved successfully")
            except Exception as e:
                self.logger.error(f"Failed to save state file: {e}")
    
    def load_results(self) -> List[Dict[str, Any]]:
        """Load previous flash sale results"""
        with self.lock:
            try:
                if Path(self.config.results_file).exists():
                    with open(self.config.results_file, 'r', encoding='utf-8') as f:
                        results = json.load(f)
                    self.logger.debug(f"Loaded {len(results)} previous results")
                    return results
                return []
            except Exception as e:
                self.logger.error(f"Failed to load results file: {e}")
                return []
    
    def save_results(self, new_results: List[Dict[str, Any]]) -> None:
        """Save flash sale results with deduplication"""
        with self.lock:
            if not new_results:
                self.logger.info("No new results to save")
                return
            
            existing_results = self.load_results()
            
            # Create hash set for deduplication
            existing_hashes = {
                self._hash_result(result) for result in existing_results
            }
            
            new_items_added = 0
            for result in new_results:
                result_hash = self._hash_result(result)
                if result_hash not in existing_hashes:
                    existing_results.append(result)
                    existing_hashes.add(result_hash)
                    new_items_added += 1
            
            if new_items_added > 0:
                try:
                    with open(self.config.results_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_results, f, ensure_ascii=False, indent=2)
                    self.logger.info(f"Saved {new_items_added} new flash sale results")
                except Exception as e:
                    self.logger.error(f"Failed to save results: {e}")
            else:
                self.logger.info("No new unique results to save")
    
    def _hash_result(self, result: Dict[str, Any]) -> str:
        """Create a hash for result deduplication"""
        # Use post_url and comment_id for uniqueness
        key = f"{result.get('post_url', '')}-{result.get('comment_id', '')}"
        return hashlib.md5(key.encode()).hexdigest()


class FlashSaleDetector:
    """Enhanced flash sale detection with improved accuracy"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.detector")
        
        # Compile regex patterns for better performance
        self.price_patterns = [
            re.compile(r'\$\d+(?:\.\d{2})?(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'\d+(?:\.\d{2})?\s*(?:usd|eur|gbp)(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'(?:from|starting|only)\s*\$?\d+', re.IGNORECASE)
        ]
        
        self.spec_patterns = [
            re.compile(r'\d+\s*(?:gb|tb|mb)\s*(?:ram|memory|storage|disk)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:core|cpu|vcpu)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:gb|tb)\s*(?:bandwidth|traffic)', re.IGNORECASE)
        ]
    
    def is_flash_sale(self, text: str) -> Dict[str, Any]:
        """Enhanced flash sale detection with confidence scoring"""
        if not text or len(text.strip()) < 10:
            return {"is_flash_sale": False, "confidence": 0.0, "reasons": []}
        
        text_lower = text.lower()
        reasons = []
        confidence = 0.0
        
        # Check for flash sale keywords
        keyword_matches = 0
        for keyword in self.config.flash_sale_keywords:
            if keyword in text_lower:
                keyword_matches += 1
                reasons.append(f"keyword: {keyword}")
        
        if keyword_matches > 0:
            confidence += min(keyword_matches * 0.1, 0.5)
        
        # Check for price patterns
        price_matches = 0
        for pattern in self.price_patterns:
            if pattern.search(text):
                price_matches += 1
                reasons.append("price_pattern")
        
        if price_matches > 0:
            confidence += min(price_matches * 0.2, 0.4)
        
        # Check for specification patterns
        spec_matches = 0
        for pattern in self.spec_patterns:
            if pattern.search(text):
                spec_matches += 1
                reasons.append("spec_pattern")
        
        if spec_matches > 0:
            confidence += min(spec_matches * 0.1, 0.3)
        
        # Boost confidence for certain combinations
        if "flash" in text_lower and "sale" in text_lower:
            confidence += 0.3
            reasons.append("flash_sale_combo")
        
        if "limited" in text_lower and ("time" in text_lower or "offer" in text_lower):
            confidence += 0.2
            reasons.append("limited_time_offer")
        
        # Reduce confidence for certain patterns that might be false positives
        if "sold out" in text_lower or "expired" in text_lower:
            confidence *= 0.5
            reasons.append("negative_indicator")
        
        is_flash_sale = confidence >= 0.3  # Threshold for classification
        
        return {
            "is_flash_sale": is_flash_sale,
            "confidence": min(confidence, 1.0),
            "reasons": reasons,
            "keyword_matches": keyword_matches,
            "price_matches": price_matches,
            "spec_matches": spec_matches
        }


class TimeUtils:
    """Utility functions for time parsing and handling"""
    
    @staticmethod
    def parse_time_string(time_str: Optional[str]) -> Optional[datetime]:
        """Parse various time string formats to datetime objects"""
        if not time_str:
            return None
        
        try:
            # Handle ISO format
            dt_obj = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            if dt_obj.tzinfo is None:
                return dt_obj.replace(tzinfo=timezone.utc)
            return dt_obj
        except ValueError:
            pass
        
        # Handle relative formats like "July 13"
        current_year = datetime.now().year
        formats_to_try = [
            f"%B %d, {current_year}",
            f"%B %d {current_year}",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d"
        ]
        
        for fmt in formats_to_try:
            try:
                return datetime.strptime(time_str, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue
        
        logging.warning(f"Could not parse time string: {time_str}")
        return None


class BrowserManager:
    """Manages browser instances and tabs with proper resource cleanup"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.browser = None
        self.logger = logging.getLogger("forum_crawler.browser")
        self.anti_detection = AntiDetectionManager(config)
    
    def __enter__(self):
        """Context manager entry"""
        self.start_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.stop_browser()
    
    def start_browser(self) -> None:
        """Initialize browser with enhanced anti-detection settings for Cloudflare bypass"""
        try:
            # Configure DrissionPage settings
            Settings.set_singleton_tab_obj(False)

            # Create ChromiumOptions for better stealth
            from DrissionPage import ChromiumOptions

            options = ChromiumOptions()

            # Essential Cloudflare bypass arguments
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            # REMOVED: --disable-javascript (Cloudflare needs JavaScript!)
            # REMOVED: --disable-images (may help with detection)

            # Additional Cloudflare bypass arguments
            options.add_argument('--disable-web-security')
            options.add_argument('--disable-features=VizDisplayCompositor')
            options.add_argument('--disable-ipc-flooding-protection')
            options.add_argument('--disable-renderer-backgrounding')
            options.add_argument('--disable-backgrounding-occluded-windows')
            options.add_argument('--disable-client-side-phishing-detection')
            options.add_argument('--disable-sync')
            options.add_argument('--disable-default-apps')
            options.add_argument('--disable-component-extensions-with-background-pages')
            options.add_argument('--disable-background-timer-throttling')
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-domain-reliability')
            options.add_argument('--disable-component-update')

            # Window and viewport settings to appear more human-like
            options.add_argument('--window-size=1920,1080')
            options.add_argument('--start-maximized')

            # Set a realistic user agent
            user_agent = self.anti_detection.get_random_user_agent()
            options.add_argument(f'--user-agent={user_agent}')

            # Critical stealth settings for Cloudflare
            options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
            options.add_experimental_option('useAutomationExtension', False)

            # Additional prefs to avoid detection
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,  # Block notifications
                    "geolocation": 2,    # Block location requests
                },
                "profile.managed_default_content_settings": {
                    "images": 1  # Allow images (important for Cloudflare)
                }
            }
            options.add_experimental_option("prefs", prefs)

            # Create browser with options
            self.browser = Chromium(addr_or_opts=options)
            self.logger.info(f"Browser started with Cloudflare bypass configuration")
            self.logger.info(f"User agent: {user_agent[:50]}...")

        except Exception as e:
            self.logger.error(f"Failed to start browser: {e}")
            # Fallback to basic browser
            try:
                self.browser = Chromium()
                self.logger.info("Browser started with basic configuration")
            except Exception as fallback_error:
                self.logger.error(f"Fallback browser creation failed: {fallback_error}")
                raise
    
    def stop_browser(self) -> None:
        """Safely stop browser and cleanup resources"""
        if self.browser:
            try:
                self.browser.quit()
                self.logger.info("Browser stopped successfully")
            except Exception as e:
                self.logger.error(f"Error stopping browser: {e}")
    
    def create_tab(self) -> Optional[ChromiumPage]:
        """Create a new browser tab with error handling"""
        try:
            if not self.browser:
                raise RuntimeError("Browser not initialized")

            tab = self.browser.new_tab()

            # Verify tab is working
            try:
                # Test basic functionality
                test_url = tab.url
                self.logger.debug(f"Created tab with URL: {test_url}")
            except Exception as test_error:
                self.logger.error(f"New tab failed basic test: {test_error}")
                return None

            # Apply anti-detection measures if the method exists
            try:
                if hasattr(tab, 'set') and hasattr(tab.set, 'user_agent'):
                    tab.set.user_agent(self.anti_detection.get_random_user_agent())
                elif hasattr(tab, 'set_user_agent'):
                    tab.set_user_agent(self.anti_detection.get_random_user_agent())
            except Exception as ua_error:
                self.logger.warning(f"Could not set user agent: {ua_error}")

            return tab
        except Exception as e:
            self.logger.error(f"Failed to create browser tab: {e}")
            return None

    def handle_cloudflare_verification(self, page: ChromiumPage, max_wait_time: int = 30) -> bool:
        """Handle Cloudflare verification if present"""
        try:
            self.logger.info("Checking for Cloudflare verification...")

            # Check for common Cloudflare indicators
            cloudflare_indicators = [
                'Checking your browser before accessing',
                'DDoS protection by Cloudflare',
                'Please wait while we verify',
                'cf-browser-verification',
                'cf-challenge-running',
                'Cloudflare'
            ]

            # Check page title and content for Cloudflare
            page_title = page.title if hasattr(page, 'title') else ""
            page_source = ""

            try:
                # Get page source safely
                page_source = page.html[:1000]  # First 1000 chars should be enough
            except Exception as e:
                self.logger.warning(f"Could not get page source: {e}")

            # Check if we're on a Cloudflare verification page
            is_cloudflare = any(indicator.lower() in page_title.lower() for indicator in cloudflare_indicators)
            is_cloudflare = is_cloudflare or any(indicator.lower() in page_source.lower() for indicator in cloudflare_indicators)

            if not is_cloudflare:
                self.logger.info("No Cloudflare verification detected")
                return True

            self.logger.info("Cloudflare verification detected, waiting for completion...")

            # Wait for verification to complete
            start_time = time.time()
            while time.time() - start_time < max_wait_time:
                try:
                    # Check if we've moved past the verification page
                    current_title = page.title if hasattr(page, 'title') else ""
                    current_url = page.url if hasattr(page, 'url') else ""

                    # If title changed and no longer contains Cloudflare indicators
                    if current_title != page_title:
                        is_still_cloudflare = any(indicator.lower() in current_title.lower() for indicator in cloudflare_indicators)
                        if not is_still_cloudflare:
                            self.logger.info("Cloudflare verification completed successfully")
                            return True

                    # Check if URL changed (successful redirect)
                    if 'lowendtalk.com' in current_url and 'cloudflare' not in current_url.lower():
                        self.logger.info("Successfully redirected past Cloudflare")
                        return True

                    # Wait a bit before checking again
                    time.sleep(2)

                except Exception as e:
                    self.logger.warning(f"Error during Cloudflare verification check: {e}")
                    time.sleep(2)

            self.logger.warning(f"Cloudflare verification timed out after {max_wait_time} seconds")
            return False

        except Exception as e:
            self.logger.error(f"Error handling Cloudflare verification: {e}")
            return False


class HealthMonitor:
    """Monitors the health of crawler components"""
    
    def __init__(self):
        self.thread_health = {}
        self.last_activity = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger("forum_crawler.health")
    
    def update_thread_health(self, thread_id: str, status: str, details: str = "") -> None:
        """Update thread health status"""
        with self.lock:
            self.thread_health[thread_id] = {
                "status": status,
                "details": details,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.last_activity[thread_id] = time.time()
    
    def check_thread_health(self, max_inactive_time: int = 300) -> Dict[str, Any]:
        """Check health of all threads"""
        with self.lock:
            current_time = time.time()
            health_report = {
                "healthy_threads": [],
                "unhealthy_threads": [],
                "inactive_threads": []
            }
            
            for thread_id, last_time in self.last_activity.items():
                time_since_activity = current_time - last_time
                thread_status = self.thread_health.get(thread_id, {})
                
                if time_since_activity > max_inactive_time:
                    health_report["inactive_threads"].append({
                        "thread_id": thread_id,
                        "inactive_time": time_since_activity,
                        "last_status": thread_status
                    })
                elif thread_status.get("status") == "error":
                    health_report["unhealthy_threads"].append({
                        "thread_id": thread_id,
                        "status": thread_status
                    })
                else:
                    health_report["healthy_threads"].append(thread_id)
            
            return health_report
    
    def log_health_summary(self) -> None:
        """Log a summary of system health"""
        health = self.check_thread_health()
        self.logger.info(f"Health Summary - Healthy: {len(health['healthy_threads'])}, "
                        f"Unhealthy: {len(health['unhealthy_threads'])}, "
                        f"Inactive: {len(health['inactive_threads'])}")


# Global instances for shared resources
health_monitor = HealthMonitor()
shutdown_event = threading.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logging.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_event.set()


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


class ForumCrawler:
    """Real-time forum crawler with efficient change detection and incremental processing"""

    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.main")
        self.state_manager = StateManager(config)
        self.detector = FlashSaleDetector(config)

        # Real-time monitoring components
        self.monitoring_active = False
        self.monitoring_thread = None
        self.change_detection_queue = queue.Queue()
        self.processing_workers = []

        # Thread synchronization
        self.state_lock = threading.RLock()
        self.results_lock = threading.Lock()
        self.shutdown_event = threading.Event()
        self.all_flash_sales_found = []

        # Monitoring configuration
        self.monitor_interval = getattr(config, 'monitor_interval', 45)  # seconds between scans
        self.last_scan_time = None
        self.consecutive_errors = 0
        self.max_consecutive_errors = 3
        
    def start_real_time_monitoring(self) -> None:
        """Start the real-time monitoring system"""
        self.logger.info("Starting real-time forum monitoring system...")

        try:
            # Initialize browser manager
            self.browser_manager = BrowserManager(self.config)
            self.browser_manager.start_browser()

            # Start monitoring thread
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(
                target=self._real_time_monitor_loop,
                name="RealTimeMonitor",
                daemon=True
            )
            self.monitoring_thread.start()

            # Start processing workers
            self._start_processing_workers()

            self.logger.info(f"Real-time monitoring started with {self.config.num_workers} workers")
            self.logger.info(f"Monitoring interval: {self.monitor_interval} seconds")

            # Keep main thread alive
            try:
                while self.monitoring_active and not self.shutdown_event.is_set():
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Received interrupt signal, shutting down...")
                self.stop_monitoring()

        except Exception as e:
            self.logger.error(f"Failed to start real-time monitoring: {e}")
            self.stop_monitoring()
            raise

    def start(self) -> None:
        """Start the crawler with real-time monitoring (legacy compatibility)"""
        self.start_real_time_monitoring()

    def _real_time_monitor_loop(self) -> None:
        """Core real-time monitoring loop - continuously checks for changes"""
        self.logger.info("Real-time monitor loop started")
        monitor_page = None

        try:
            # Create dedicated monitoring page
            monitor_page = self.browser_manager.create_tab()
            if not monitor_page:
                self.logger.error("Failed to create monitoring page")
                return

            while self.monitoring_active and not self.shutdown_event.is_set():
                try:
                    scan_start_time = time.time()
                    self.logger.info(f"Starting real-time scan at {datetime.now().strftime('%H:%M:%S')}")

                    # Perform change detection scan
                    changes_detected = self._detect_comment_changes(monitor_page)

                    scan_duration = time.time() - scan_start_time
                    self.logger.info(f"Scan completed in {scan_duration:.2f}s, detected {changes_detected} changes")

                    # Reset error counter on successful scan
                    self.consecutive_errors = 0
                    self.last_scan_time = time.time()

                    # Wait for next scan interval
                    self._wait_for_next_scan()

                except Exception as e:
                    self.consecutive_errors += 1
                    self.logger.error(f"Monitor scan error ({self.consecutive_errors}/{self.max_consecutive_errors}): {e}")

                    if self.consecutive_errors >= self.max_consecutive_errors:
                        self.logger.error("Too many consecutive errors, stopping monitoring")
                        break

                    # Wait before retry
                    time.sleep(min(30, self.monitor_interval))

        except Exception as e:
            self.logger.error(f"Critical error in monitor loop: {e}")
        finally:
            if monitor_page:
                try:
                    monitor_page.close()
                except:
                    pass
            self.logger.info("Real-time monitor loop ended")

    def _detect_comment_changes(self, monitor_page) -> int:
        """Detect changes in comment counts and queue posts for processing"""
        changes_detected = 0

        try:
            # Navigate to forum main page
            forum_url = "https://lowendtalk.com/categories/offers"
            monitor_page.get(forum_url)
            time.sleep(3)

            # Handle Cloudflare if present
            if not self.browser_manager.handle_cloudflare_verification(monitor_page, max_wait_time=30):
                self.logger.warning("Cloudflare verification failed during monitoring")
                return 0

            # Load current state
            with self.state_lock:
                current_state = self.state_manager.load_state()
                processed_posts = current_state.get("processed_posts", {})

            # Extract posts from page
            posts = self._extract_posts_from_page(monitor_page)
            self.logger.info(f"Found {len(posts)} posts on forum page")

            for post_data in posts:
                try:
                    post_url = post_data["post_url"]
                    current_comment_count = post_data["current_comment_count"]
                    post_title = post_data["post_title"][:50]

                    # Check if this is a new post or has comment changes
                    change_type = self._analyze_post_changes(post_data, processed_posts)

                    if change_type == "new_post":
                        if self._should_process_new_post(post_data):
                            self.logger.info(f"NEW POST: {post_title}... ({current_comment_count} comments)")
                            self._queue_for_processing(post_data, "new_post")
                            self._record_new_post_in_state(post_data)
                            changes_detected += 1
                        else:
                            self.logger.debug(f"NEW POST SKIPPED: {post_title}... ({current_comment_count} < {self.config.min_comments_for_new_posts})")

                    elif change_type == "comment_increase":
                        prev_count = processed_posts[post_url]["last_comment_count"]
                        increase = current_comment_count - prev_count
                        self.logger.info(f"COMMENT INCREASE: {post_title}... ({prev_count} -> {current_comment_count}, +{increase})")
                        self._queue_for_processing(post_data, "comment_increase")
                        changes_detected += 1

                    elif change_type == "no_change":
                        self.logger.debug(f"NO CHANGE: {post_title}... ({current_comment_count} comments)")

                except Exception as e:
                    self.logger.error(f"Error analyzing post: {e}")
                    continue

            return changes_detected

        except Exception as e:
            self.logger.error(f"Error in change detection: {e}")
            return 0

    def _analyze_post_changes(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> str:
        """Analyze what type of change occurred for a post"""
        post_url = post_data["post_url"]
        current_comment_count = post_data["current_comment_count"]

        if post_url not in processed_posts:
            return "new_post"

        prev_count = processed_posts[post_url].get("last_comment_count", 0)

        if current_comment_count > prev_count:
            return "comment_increase"
        elif current_comment_count == prev_count:
            return "no_change"
        else:
            # Comment count decreased (unusual, might be deletion)
            return "comment_decrease"

    def _should_process_new_post(self, post_data: Dict[str, Any]) -> bool:
        """Determine if a new post should be processed based on comment threshold"""
        current_comment_count = post_data["current_comment_count"]
        min_comments = getattr(self.config, 'min_comments_for_new_posts', 5)
        return current_comment_count >= min_comments

    def _queue_for_processing(self, post_data: Dict[str, Any], change_type: str) -> None:
        """Queue a post for detailed processing"""
        processing_task = {
            **post_data,
            "change_type": change_type,
            "queued_at": datetime.now().isoformat(),
            "priority": "high" if change_type == "comment_increase" else "normal"
        }

        self.change_detection_queue.put(processing_task)
        queue_size = self.change_detection_queue.qsize()
        self.logger.debug(f"Queued for processing: {post_data['post_title'][:30]}... (Queue: {queue_size})")

    def _record_new_post_in_state(self, post_data: Dict[str, Any]) -> None:
        """Record a new post in the state file to prevent reprocessing"""
        with self.state_lock:
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})

            processed_posts[post_data["post_url"]] = {
                "last_comment_count": post_data["current_comment_count"],
                "last_comment_datetime": post_data["datetime_attr"],
                "processed_comment_ids": [],
                "last_comment_content": "",
                "first_detected_at": datetime.now().isoformat(),
                "status": "recorded",
                "post_title": post_data["post_title"]
            }

            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)

            self.logger.debug(f"Recorded new post in state: {post_data['post_title'][:30]}...")

    def _start_processing_workers(self) -> None:
        """Start worker threads for processing detected changes"""
        self.processing_workers = []

        for i in range(self.config.num_workers):
            worker_id = f"ProcessWorker-{i+1}"
            worker_thread = threading.Thread(
                target=self._processing_worker_loop,
                args=(worker_id,),
                name=worker_id,
                daemon=True
            )
            worker_thread.start()
            self.processing_workers.append(worker_thread)

        self.logger.info(f"Started {len(self.processing_workers)} processing workers")

    def _processing_worker_loop(self, worker_id: str) -> None:
        """Worker loop for processing posts with detected changes"""
        self.logger.info(f"[{worker_id}] Processing worker started")
        worker_page = None

        try:
            # Create dedicated page for this worker
            worker_page = self.browser_manager.create_tab()
            if not worker_page:
                self.logger.error(f"[{worker_id}] Failed to create worker page")
                return

            while self.monitoring_active and not self.shutdown_event.is_set():
                try:
                    # Get processing task
                    task = self.change_detection_queue.get(timeout=10)

                    if task is None:  # Shutdown signal
                        break

                    # Process the task
                    self._process_detected_change(worker_id, worker_page, task)
                    self.change_detection_queue.task_done()

                except queue.Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing task: {e}")
                    try:
                        self.change_detection_queue.task_done()
                    except:
                        pass
                    continue

        except Exception as e:
            self.logger.error(f"[{worker_id}] Critical worker error: {e}")
        finally:
            if worker_page:
                try:
                    worker_page.close()
                except:
                    pass
            self.logger.info(f"[{worker_id}] Processing worker ended")

    def _process_detected_change(self, worker_id: str, worker_page, task: Dict[str, Any]) -> None:
        """Process a post with detected changes using incremental processing"""
        post_url = task["post_url"]
        post_title = task["post_title"][:50]
        change_type = task["change_type"]
        current_comment_count = task["current_comment_count"]

        self.logger.info(f"[{worker_id}] Processing {change_type}: {post_title}... ({current_comment_count} comments)")

        try:
            # Navigate to the post detail page
            if not self._navigate_to_post_page(worker_id, worker_page, post_url, current_comment_count):
                self.logger.error(f"[{worker_id}] Failed to navigate to post page")
                return

            # Get current state for incremental processing
            with self.state_lock:
                current_state = self.state_manager.load_state()
                processed_posts = current_state.get("processed_posts", {})
                prev_state = processed_posts.get(post_url, {})

            # Perform incremental comment processing
            processing_result = self._process_comments_incrementally(
                worker_id, worker_page, task, prev_state
            )

            if processing_result:
                # Update state with new information
                self._update_post_state_after_processing(
                    worker_id, post_url, processing_result, task
                )

                self.logger.info(f"[{worker_id}] Successfully processed {change_type}: {post_title}...")
            else:
                self.logger.warning(f"[{worker_id}] No results from processing: {post_title}...")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error processing {change_type}: {e}")

    def _navigate_to_post_page(self, worker_id: str, worker_page, post_url: str, comment_count: int) -> bool:
        """Navigate to the appropriate page of the post (last page for multi-page posts)"""
        try:
            # Calculate target page for multi-page posts
            if comment_count > 30:
                import math
                final_page = math.ceil(comment_count / 30)
                base_url = post_url.split('/p')[0]
                target_url = f"{base_url}/p{final_page}"
                self.logger.info(f"[{worker_id}] Navigating to final page {final_page}: {target_url}")
            else:
                target_url = post_url
                self.logger.info(f"[{worker_id}] Navigating to single page: {target_url}")

            # Navigate to target page
            worker_page.get(target_url)
            time.sleep(3)

            # Handle Cloudflare if present
            if not self.browser_manager.handle_cloudflare_verification(worker_page, max_wait_time=30):
                self.logger.warning(f"[{worker_id}] Cloudflare verification failed")
                return False

            # Verify page loaded correctly
            current_url = worker_page.url
            if "lowendtalk.com" not in current_url:
                self.logger.error(f"[{worker_id}] Invalid page loaded: {current_url}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"[{worker_id}] Navigation error: {e}")
            return False

    def _process_comments_incrementally(self, worker_id: str, worker_page, task: Dict[str, Any], prev_state: Dict[str, Any]) -> Dict[str, Any]:
        """Process only new comments using incremental processing"""
        try:
            # Get all comments from current page
            comments = self._get_comments_from_page(worker_page)
            if not comments:
                self.logger.warning(f"[{worker_id}] No comments found on page")
                return None

            self.logger.info(f"[{worker_id}] Found {len(comments)} comments on current page")

            # Get previous state information
            last_known_content = prev_state.get("last_comment_content", "")
            prev_processed_ids = set(prev_state.get("processed_comment_ids", []))

            # Process comments incrementally (newest first)
            new_comments_processed = 0
            new_flash_sales = []
            new_processed_ids = set()
            last_comment_content = ""

            # Reverse comments to start from newest
            comments_to_check = list(reversed(comments))

            for comment in comments_to_check:
                try:
                    comment_id = comment.attr('id') or f"comment_{int(time.time())}"
                    comment_text_element = comment.ele('.Message')

                    if not comment_text_element:
                        continue

                    comment_text = comment_text_element.text.strip()
                    if len(comment_text) < 10:
                        continue

                    # Store the first (newest) comment content
                    if not last_comment_content:
                        last_comment_content = comment_text[:200]

                    # INCREMENTAL PROCESSING: Stop when we reach known content
                    if last_known_content and comment_text[:200] == last_known_content:
                        self.logger.info(f"[{worker_id}] Reached last known comment, stopping incremental processing")
                        break

                    # Skip if already processed
                    if comment_id in prev_processed_ids:
                        continue

                    # This is a new comment - process it
                    new_comments_processed += 1
                    new_processed_ids.add(comment_id)

                    # Check for flash sale
                    detection_result = self.detector.is_flash_sale(comment_text)
                    if detection_result["is_flash_sale"]:
                        flash_sale_data = self._create_flash_sale_record(
                            task, comment, comment_text, detection_result
                        )
                        new_flash_sales.append(flash_sale_data)
                        self.logger.info(f"[{worker_id}] Flash sale detected in new comment! "
                                       f"Confidence: {detection_result['confidence']:.2f}")

                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing comment: {e}")
                    continue

            # Return processing results
            result = {
                "new_comments_processed": new_comments_processed,
                "new_flash_sales": new_flash_sales,
                "new_processed_ids": new_processed_ids,
                "last_comment_content": last_comment_content,
                "total_comments_on_page": len(comments),
                "actual_comment_count": task["current_comment_count"]  # Use monitor's detected count
            }

            self.logger.info(f"[{worker_id}] Incremental processing complete: "
                           f"{new_comments_processed} new comments, {len(new_flash_sales)} flash sales")

            return result

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error in incremental processing: {e}")
            return None

    def _update_post_state_after_processing(self, worker_id: str, post_url: str, result: Dict[str, Any], task: Dict[str, Any]) -> None:
        """Update state file with processing results"""
        try:
            with self.state_lock:
                current_state = self.state_manager.load_state()
                processed_posts = current_state.get("processed_posts", {})

                # Get previous state
                prev_state = processed_posts.get(post_url, {})
                prev_processed_ids = set(prev_state.get("processed_comment_ids", []))

                # Combine previous and new processed IDs
                all_processed_ids = prev_processed_ids.union(result["new_processed_ids"])

                # Update state with new information
                processed_posts[post_url] = {
                    "last_comment_count": result["actual_comment_count"],
                    "last_comment_datetime": task["datetime_attr"],
                    "processed_comment_ids": list(all_processed_ids),
                    "last_comment_content": result["last_comment_content"],
                    "last_processed_at": datetime.now().isoformat(),
                    "status": "processed",
                    "post_title": task["post_title"],
                    "new_comments_in_last_run": result["new_comments_processed"],
                    "total_flash_sales_found": len(result["new_flash_sales"])
                }

                current_state["processed_posts"] = processed_posts
                self.state_manager.save_state(current_state)

                # Verify state was saved
                verification_state = self.state_manager.load_state()
                verification_count = verification_state.get("processed_posts", {}).get(post_url, {}).get("last_comment_count", 0)

                if verification_count == result["actual_comment_count"]:
                    self.logger.info(f"[{worker_id}] STATE UPDATED SUCCESSFULLY: "
                                   f"Comments: {prev_state.get('last_comment_count', 0)} -> {result['actual_comment_count']} "
                                   f"(+{result['new_comments_processed']} new)")
                else:
                    self.logger.error(f"[{worker_id}] STATE UPDATE FAILED: "
                                    f"Expected {result['actual_comment_count']}, verified {verification_count}")

                # Save flash sales if found
                if result["new_flash_sales"]:
                    with self.results_lock:
                        self.all_flash_sales_found.extend(result["new_flash_sales"])
                        self.state_manager.save_results(result["new_flash_sales"])

                    self.logger.info(f"[{worker_id}] Saved {len(result['new_flash_sales'])} new flash sales")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error updating state: {e}")

    def _get_comments_from_page(self, page) -> List:
        """Get all comments from the current page"""
        try:
            # Try multiple comment selectors
            comment_selectors = ['.Comment', '.CommentWrap', '.ItemComment', '[class*="Comment"]']

            for selector in comment_selectors:
                comments = page.eles(selector)
                if comments:
                    return comments

            return []

        except Exception as e:
            self.logger.error(f"Error getting comments from page: {e}")
            return []

    def _extract_posts_from_page(self, page) -> List[Dict[str, Any]]:
        """Extract post data from the forum page"""
        posts = []

        try:
            # Wait for page to load
            time.sleep(2)

            # Try multiple selectors for post items
            post_selectors = [
                'xpath:./li[contains(@class, "Item")]',
                '.Item',
                '.DiscussionRow',
                '[class*="Item"]'
            ]

            post_elements = []
            for selector in post_selectors:
                try:
                    elements = page.eles(selector)
                    if elements:
                        post_elements = elements
                        self.logger.debug(f"Found {len(elements)} posts using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Selector {selector} failed: {e}")
                    continue

            if not post_elements:
                self.logger.warning("No post elements found on page")
                return []

            # Extract data from each post
            for i, item in enumerate(post_elements):
                try:
                    post_data = self._extract_post_data(item)
                    if post_data:
                        posts.append(post_data)
                    else:
                        self.logger.debug(f"Could not extract data from post {i+1}")

                except Exception as e:
                    self.logger.debug(f"Error extracting post {i+1}: {e}")
                    continue

            self.logger.info(f"Successfully extracted {len(posts)} posts from page")
            return posts

        except Exception as e:
            self.logger.error(f"Error extracting posts from page: {e}")
            return []

    def _extract_post_data(self, item) -> Dict[str, Any]:
        """Extract data from a single post item"""
        try:
            # Extract post title and URL
            title_element = item.ele('.Title a') or item.ele('a[href*="/discussion/"]')
            if not title_element:
                return None

            post_title = title_element.text.strip()
            post_url = title_element.attr('href')

            if not post_title or not post_url:
                return None

            # Ensure URL is absolute
            if post_url.startswith('/'):
                post_url = f"https://lowendtalk.com{post_url}"

            # Extract comment count
            comment_count = 0
            comment_selectors = [
                '.Count',
                '.CommentCount',
                '[class*="Count"]',
                '.Meta .Count'
            ]

            for selector in comment_selectors:
                try:
                    count_element = item.ele(selector)
                    if count_element:
                        count_text = count_element.text.strip()
                        # Extract number from text like "25 comments" or just "25"
                        import re
                        count_match = re.search(r'(\d+)', count_text)
                        if count_match:
                            comment_count = int(count_match.group(1))
                            break
                except Exception:
                    continue

            # Extract datetime
            datetime_attr = ""
            datetime_selectors = [
                '.DateTimeOriginal',
                '.Meta .DateTime',
                '[title*="20"]',  # Look for elements with year in title
                '.DateTime'
            ]

            for selector in datetime_selectors:
                try:
                    datetime_element = item.ele(selector)
                    if datetime_element:
                        datetime_attr = datetime_element.attr('title') or datetime_element.text.strip()
                        if datetime_attr:
                            break
                except Exception:
                    continue

            # If no datetime found, use current time
            if not datetime_attr:
                datetime_attr = datetime.now().isoformat()

            return {
                "post_title": post_title,
                "post_url": post_url,
                "current_comment_count": comment_count,
                "datetime_attr": datetime_attr
            }

        except Exception as e:
            self.logger.debug(f"Error extracting post data: {e}")
            return None

    def _wait_for_next_scan(self) -> None:
        """Wait for the next monitoring scan with intelligent timing"""
        try:
            # Calculate next scan time
            if self.last_scan_time:
                elapsed = time.time() - self.last_scan_time
                remaining = max(0, self.monitor_interval - elapsed)
            else:
                remaining = self.monitor_interval

            if remaining > 0:
                self.logger.debug(f"Waiting {remaining:.1f}s until next scan")

                # Wait in small increments to allow for shutdown
                while remaining > 0 and self.monitoring_active and not self.shutdown_event.is_set():
                    sleep_time = min(1, remaining)
                    time.sleep(sleep_time)
                    remaining -= sleep_time

        except Exception as e:
            self.logger.error(f"Error in wait timing: {e}")

    def _create_flash_sale_record(self, task: Dict[str, Any], comment, comment_text: str, detection_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create a flash sale record from detected comment"""
        try:
            comment_id = comment.attr('id') or f"comment_{int(time.time())}"

            # Try to get comment author and datetime
            author_element = comment.ele('.Author')
            author = author_element.text.strip() if author_element else "Unknown"

            datetime_element = comment.ele('.DateTimeOriginal')
            comment_datetime = datetime_element.attr('title') if datetime_element else datetime.now().isoformat()

            return {
                "post_url": task["post_url"],
                "post_title": task["post_title"],
                "comment_id": comment_id,
                "comment_text": comment_text,
                "comment_author": author,
                "comment_datetime": comment_datetime,
                "confidence": detection_result["confidence"],
                "keywords_found": detection_result["keywords_found"],
                "detected_at": datetime.now().isoformat(),
                "detection_method": "incremental_processing"
            }

        except Exception as e:
            self.logger.error(f"Error creating flash sale record: {e}")
            return {}

    def stop_monitoring(self) -> None:
        """Stop the real-time monitoring system"""
        self.logger.info("Stopping real-time monitoring...")

        self.monitoring_active = False
        self.shutdown_event.set()

        # Stop monitoring thread
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=10)

        # Stop processing workers
        for _ in self.processing_workers:
            try:
                self.change_detection_queue.put(None)  # Shutdown signal
            except:
                pass

        for worker in self.processing_workers:
            if worker.is_alive():
                worker.join(timeout=5)

        # Cleanup browser
        if self.browser_manager:
            self.browser_manager.cleanup()

        # Save final results
        if self.all_flash_sales_found:
            try:
                self.state_manager.save_results(self.all_flash_sales_found)
                self.logger.info(f"Saved {len(self.all_flash_sales_found)} total flash sales")
            except Exception as e:
                self.logger.error(f"Error saving final results: {e}")

        self.logger.info("Real-time monitoring stopped")
        self.logger.info("Starting Forum Crawler...")
        
        try:
            with BrowserManager(self.config) as browser_manager:
                self._start_threads(browser_manager)
                self._run_main_loop()
        except Exception as e:
            self.logger.error(f"Critical error in crawler: {e}")
        finally:
            self._cleanup()
    
    def _start_threads(self, browser_manager: BrowserManager) -> None:
        """Start monitor and worker threads"""
        # Start monitor thread
        self.monitor_thread = threading.Thread(
            target=self._monitor_thread_func,
            args=(browser_manager,),
            name="Monitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        # Start worker threads
        for i in range(self.config.num_workers):
            worker_thread = threading.Thread(
                target=self._worker_thread_func,
                args=(f"Worker-{i+1}", browser_manager),
                name=f"Worker-{i+1}",
                daemon=True
            )
            worker_thread.start()
            self.worker_threads.append(worker_thread)
        
        self.logger.info(f"Started {self.config.num_workers} worker threads and 1 monitor thread")
    
    def _run_main_loop(self) -> None:
        """Main loop for monitoring and health checks"""
        health_check_interval = 60  # Check health every minute
        last_health_check = 0
        
        while not shutdown_event.is_set():
            try:
                current_time = time.time()
                
                # Periodic health checks
                if current_time - last_health_check > health_check_interval:
                    health_monitor.log_health_summary()
                    last_health_check = current_time
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt")
                break
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                time.sleep(5)
    
    def _cleanup(self) -> None:
        """Cleanup resources and save final state"""
        self.logger.info("Starting cleanup process...")
        
        # Signal shutdown to all threads
        shutdown_event.set()
        
        # Wait for threads to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=10)
        
        # Save final results
        with self.results_lock:
            if self.all_flash_sales_found:
                self.state_manager.save_results(self.all_flash_sales_found)
                self.logger.info(f"Saved {len(self.all_flash_sales_found)} flash sale results")
        
        # Save final state
        final_state = self.state_manager.load_state()
        self.state_manager.save_state(final_state)
        
        self.logger.info("Cleanup completed")
    
    def _monitor_thread_func(self, browser_manager: BrowserManager) -> None:
        """Monitor thread function - tracks forum for updates"""
        thread_id = "monitor"
        self.browser_manager = browser_manager  # Store reference for Cloudflare handling
        health_monitor.update_thread_health(thread_id, "starting", "Initializing monitor thread")

        try:
            monitor_page = browser_manager.create_tab()
            if not monitor_page:
                raise RuntimeError("Failed to create monitor page")

            # Perform initial scan with timeout protection
            try:
                self._perform_initial_scan(monitor_page, thread_id)
                self.monitor_ready_event.set()
                self.logger.info("Monitor thread ready event set - workers can now start")
            except Exception as e:
                self.logger.error(f"Initial scan failed: {e}")
                # Set the event anyway to prevent workers from hanging
                self.monitor_ready_event.set()
                raise

            # Continuous monitoring loop
            while not shutdown_event.is_set():
                try:
                    self._perform_monitoring_cycle(monitor_page, thread_id)
                    time.sleep(self.config.refresh_interval)
                except Exception as e:
                    self.logger.error(f"Error in monitoring cycle: {e}")
                    health_monitor.update_thread_health(thread_id, "error", str(e))
                    time.sleep(30)  # Wait before retrying

        except Exception as e:
            self.logger.error(f"Critical error in monitor thread: {e}")
            health_monitor.update_thread_health(thread_id, "critical_error", str(e))
            # Always set the ready event to prevent workers from hanging
            self.monitor_ready_event.set()
        finally:
            health_monitor.update_thread_health(thread_id, "stopped", "Monitor thread ended")
            self.logger.info("Monitor thread ended")
    
    def _perform_initial_scan(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform initial scan of the forum"""
        health_monitor.update_thread_health(thread_id, "initial_scan", "Loading forum page")

        try:
            self.logger.info(f"Loading forum page: {self.config.monitor_url}")
            monitor_page.get(self.config.monitor_url)

            # Wait for page to load
            self.logger.info("Waiting for page to load...")
            time.sleep(self.config.page_load_delay)

            # Handle Cloudflare verification if present
            if not self.browser_manager.handle_cloudflare_verification(monitor_page, max_wait_time=60):
                raise RuntimeError("Failed to pass Cloudflare verification")

            # Additional wait after Cloudflare verification
            time.sleep(3)

            # Try multiple selectors to find posts container
            posts_container = None
            selectors_to_try = [
                'tag:ul@class=DataList Discussions',
                '.DataList.Discussions',
                'ul.DataList.Discussions',
                '.ItemDiscussion'  # Try to find individual posts if container not found
            ]

            for selector in selectors_to_try:
                self.logger.info(f"Trying selector: {selector}")
                try:
                    posts_container = monitor_page.ele(selector, timeout=10)
                    if posts_container:
                        self.logger.info(f"Found posts container with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not posts_container:
                # Log page content for debugging
                self.logger.error("Could not find posts container. Page title: " +
                                (monitor_page.title if hasattr(monitor_page, 'title') else "Unknown"))

                # Try a more basic approach - just look for any posts
                try:
                    # Check if we can find any discussion items at all
                    any_posts = monitor_page.eles('.ItemDiscussion')
                    if any_posts:
                        self.logger.info(f"Found {len(any_posts)} individual posts, proceeding without container")
                        # Create a mock container for processing
                        posts_container = monitor_page  # Use the page itself as container
                    else:
                        raise RuntimeError("No posts found on page - site may be down or structure changed")
                except Exception as e:
                    self.logger.error(f"Failed to find any posts: {e}")
                    raise RuntimeError("Could not find any posts on the forum page")

            health_monitor.update_thread_health(thread_id, "scanning", "Processing initial posts")
            self._process_posts(posts_container, thread_id, is_initial=True)

            health_monitor.update_thread_health(thread_id, "ready", "Initial scan completed")
            self.logger.info("Initial scan completed, monitor thread ready")

        except Exception as e:
            self.logger.error(f"Error in initial scan: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Initial scan failed: {e}")
            raise
    
    def _perform_monitoring_cycle(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform a single monitoring cycle"""
        health_monitor.update_thread_health(thread_id, "refreshing", "Refreshing forum page")
        
        monitor_page.refresh()
        time.sleep(self.config.page_load_delay)
        
        posts_container = monitor_page.ele('tag:ul@class=DataList Discussions', timeout=30)
        if not posts_container:
            self.logger.warning("Could not find posts container after refresh")
            return
        
        health_monitor.update_thread_health(thread_id, "processing", "Processing updated posts")
        self._process_posts(posts_container, thread_id, is_initial=False)
        
        health_monitor.update_thread_health(thread_id, "active", "Monitoring cycle completed")
    
    def _process_posts(self, posts_container, thread_id: str, is_initial: bool = False) -> None:
        """Process posts from the container"""
        try:
            # Get all discussion items - try multiple approaches
            all_items = []

            # Try different selectors to find discussion items
            # Based on debug analysis: actual classes are "Item Unread ItemDiscussion ItemDiscussion-withPhoto"
            selectors_to_try = [
                'xpath:./li[contains(@class, "Item")]',  # Direct children with Item class
                'li.Item',  # Li elements with Item class
                'xpath:.//li[contains(@class, "ItemDiscussion")]',  # Fallback to original
                '.Item'  # Any element with Item class
            ]

            for selector in selectors_to_try:
                try:
                    items = posts_container.eles(selector)
                    if items:
                        all_items = items
                        self.logger.info(f"Found {len(all_items)} items using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not all_items:
                self.logger.warning("No discussion items found, trying fallback approach")
                # Fallback: try to find any items that might be posts
                try:
                    all_items = posts_container.eles('li')
                    self.logger.info(f"Fallback found {len(all_items)} li elements")
                except Exception as e:
                    self.logger.error(f"Fallback approach failed: {e}")
                    return

            # Filter out announcements - use faster approach
            non_announcement_posts = []
            for i, item in enumerate(all_items):
                try:
                    # Use a faster method - check if class contains announcement
                    item_classes = item.attr('class') or ''
                    if 'announcement' not in item_classes.lower():
                        non_announcement_posts.append(item)
                        if i < 5:  # Log first few for debugging
                            self.logger.debug(f"Item {i+1} classes: {item_classes}")
                    else:
                        self.logger.debug(f"Skipping announcement item {i+1}")
                except Exception as e:
                    # If we can't check for announcements, include the item anyway
                    self.logger.warning(f"Could not check announcement status for item {i+1}: {e}")
                    non_announcement_posts.append(item)

            # Limit posts to check
            posts_to_check = non_announcement_posts[:self.config.max_posts_to_check]

            scan_type = "Initial" if is_initial else "Update"
            self.logger.info(f"{scan_type} scan found {len(posts_to_check)} posts to check")

            if not posts_to_check:
                self.logger.warning("No posts to check after filtering")
                return

            # Load current state (force reload to get latest updates from workers)
            self.logger.info("Loading current state...")
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            self.logger.info(f"Loaded state with {len(processed_posts)} previously processed posts")

            # Add a small delay to ensure any pending state updates are written
            time.sleep(0.1)

            for i, item in enumerate(posts_to_check):
                try:
                    self.logger.info(f"Processing post {i+1}/{len(posts_to_check)}")
                    post_data = self._extract_post_data(item)
                    if not post_data:
                        self.logger.warning(f"Could not extract data from post {i+1}")
                        continue

                    safe_title = post_data['post_title'][:50].encode('ascii', 'ignore').decode('ascii')
                    self.logger.info(f"Extracted post: {safe_title}...")

                    # Reload state for each post to get latest updates from workers
                    current_state = self.state_manager.load_state()
                    processed_posts = current_state.get("processed_posts", {})

                    should_queue = self._should_process_post(post_data, processed_posts)

                    if should_queue:
                        self.task_queue.put(post_data)
                        queue_size = self.task_queue.qsize()
                        self.logger.info(f"Queued post: {safe_title}... (Queue size: {queue_size})")

                        # Update the processed_posts immediately to prevent re-queuing
                        # This is a temporary marker that will be updated by worker with actual data
                        processed_posts[post_data["post_url"]] = {
                            "last_comment_count": post_data["current_comment_count"],
                            "last_comment_datetime": post_data["datetime_attr"],
                            "processed_comment_ids": [],
                            "queued_at": datetime.now().isoformat(),
                            "status": "queued"  # Marker to indicate this is queued, not fully processed
                        }
                    else:
                        # Update state for unchanged posts
                        self._update_post_state(post_data, processed_posts)
                        self.logger.debug(f"Skipped unchanged post: {post_data['post_title'][:50]}...")

                except Exception as e:
                    self.logger.error(f"Error processing post item {i+1}: {e}")
                    continue

            # Save updated state
            self.logger.info("Saving updated state...")
            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)

            # DEBUG: Log queue and worker status
            queue_size = self.task_queue.qsize()
            self.logger.info(f"Post processing completed for {scan_type} scan. Queue size: {queue_size}")

            # Log worker thread status
            active_workers = sum(1 for t in threading.enumerate() if t.name.startswith("Worker-"))
            self.logger.info(f"Active worker threads: {active_workers}")

            # CRITICAL: Wait for workers to complete and force reload state
            if queue_size > 0:
                self.logger.info(f"Waiting for {queue_size} queued tasks to complete...")
                # Wait a reasonable time for workers to process
                max_wait_time = min(queue_size * 10, 60)  # Max 60 seconds
                start_wait = time.time()

                while self.task_queue.qsize() > 0 and (time.time() - start_wait) < max_wait_time:
                    time.sleep(1)

                final_queue_size = self.task_queue.qsize()
                if final_queue_size == 0:
                    self.logger.info("All queued tasks completed, forcing state reload...")
                    # Force reload state to get latest updates from workers
                    time.sleep(1)  # Brief delay to ensure all writes complete
                    current_state = self.state_manager.load_state()
                    processed_posts = current_state.get("processed_posts", {})
                    self.logger.info(f"State reloaded: {len(processed_posts)} processed posts")
                else:
                    self.logger.warning(f"Timeout waiting for tasks: {final_queue_size} tasks still pending")

        except Exception as e:
            self.logger.error(f"Error in _process_posts: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Post processing error: {e}")
            raise  # Re-raise to ensure the error is handled properly

    def _extract_post_data(self, item) -> Optional[Dict[str, Any]]:
        """Extract post data from a forum item"""
        try:
            # Use the working approach from quick_fix_crawler.py
            # Try to find title link directly (this works!)
            title_link = item.ele('a', timeout=2)
            if not title_link:
                self.logger.debug("No title link found")
                return None

            post_title = title_link.text.strip()
            post_url = title_link.link

            if not post_title or not post_url:
                self.logger.debug("Missing title or URL")
                return None

            # Ensure absolute URL
            if not post_url.startswith('http'):
                post_url = self.config.base_url.rstrip('/') + post_url

            # Extract comment count using the working method
            current_comment_count = 0
            try:
                # Look for comment count in various places
                count_elements = item.eles('@@class:Number@@title:comments')
                for elem in count_elements:
                    text = elem.text.strip()
                    if text.isdigit():
                        current_comment_count = int(text)
                        break
                    elif 'k' in text.lower():
                        try:
                            num = float(text.lower().replace('k', ''))
                            current_comment_count = int(num * 1000)
                            break
                        except:
                            pass
            except Exception as e:
                self.logger.debug(f"Could not extract comment count: {e}")

            # Extract last comment date - simplified approach
            datetime_attr = None
            try:
                time_elements = item.eles('tag:time')
                if time_elements:
                    datetime_attr = time_elements[0].attr('datetime')
            except Exception as e:
                self.logger.debug(f"Could not extract datetime: {e}")

            # If we got here, we have at least title and URL
            # Use safe logging to avoid Unicode errors
            safe_title = post_title[:50].encode('ascii', 'ignore').decode('ascii')
            self.logger.info(f"Extracted: {safe_title}... (Comments: {current_comment_count})")

            return {
                "post_url": post_url,
                "post_title": post_title,
                "current_comment_count": current_comment_count,
                "datetime_attr": datetime_attr or datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error extracting post data: {e}")
            return None

    def _should_process_post(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> bool:
        """Determine if a post should be processed with smart filtering"""
        post_url = post_data["post_url"]
        current_comment_count = post_data["current_comment_count"]
        datetime_attr = post_data["datetime_attr"]
        post_title = post_data["post_title"][:50]

        # NEW POST PROCESSING STRATEGY
        if post_url not in processed_posts:
            # Apply minimum comment threshold for new posts
            min_comments_for_new_posts = getattr(self.config, 'min_comments_for_new_posts', 5)

            if current_comment_count < min_comments_for_new_posts:
                self.logger.debug(f"New post skipped (too few comments): {post_title}... "
                                f"({current_comment_count} < {min_comments_for_new_posts})")
                return False

            self.logger.info(f"New post detected (sufficient comments): {post_title}... "
                           f"({current_comment_count} comments)")
            return True

        # EXISTING POST PROCESSING STRATEGY
        prev_state = processed_posts[post_url]
        prev_comment_count = prev_state.get("last_comment_count", 0)
        prev_datetime_str = prev_state.get("last_comment_datetime")

        # Check comment count increase (must be significant to avoid noise)
        if current_comment_count > prev_comment_count:
            increase = current_comment_count - prev_comment_count
            self.logger.info(f"Comment count increased: {prev_comment_count} -> {current_comment_count} (+{increase})")

            # Only process if increase is significant (at least 1 comment)
            if increase >= 1:
                return True
            else:
                self.logger.debug(f"Comment increase too small ({increase}), skipping")

        # Check timestamp update
        if datetime_attr and prev_datetime_str:
            current_datetime = TimeUtils.parse_time_string(datetime_attr)
            prev_datetime = TimeUtils.parse_time_string(prev_datetime_str)

            if current_datetime and prev_datetime and current_datetime > prev_datetime:
                self.logger.info(f"Timestamp updated: {prev_datetime_str} -> {datetime_attr}")
                return True

        # Log why we're not processing
        self.logger.debug(f"No changes detected for post: {post_title}... "
                         f"(comments: {current_comment_count}, prev: {prev_comment_count})")
        return False

    def _update_post_state(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> None:
        """Update state for posts that don't need processing"""
        post_url = post_data["post_url"]
        prev_state = processed_posts.get(post_url, {})

        processed_posts[post_url] = {
            "last_comment_count": post_data["current_comment_count"],
            "last_comment_datetime": post_data["datetime_attr"],
            "processed_comment_ids": prev_state.get("processed_comment_ids", [])
        }

    def _worker_thread_func(self, worker_id: str, browser_manager: BrowserManager) -> None:
        """Worker thread function - processes posts from the queue"""
        health_monitor.update_thread_health(worker_id, "starting", "Initializing worker thread")

        try:
            worker_page = browser_manager.create_tab()
            if not worker_page:
                raise RuntimeError("Failed to create worker page")

            # Wait for monitor to be ready
            self.logger.info(f"[{worker_id}] Waiting for monitor thread (timeout: {self.config.monitor_timeout}s)...")
            if not self.monitor_ready_event.wait(timeout=self.config.monitor_timeout):
                self.logger.error(f"[{worker_id}] Monitor thread not ready after {self.config.monitor_timeout}s, worker exiting")
                return

            self.logger.info(f"[{worker_id}] Monitor ready! Worker thread starting main loop")
            health_monitor.update_thread_health(worker_id, "ready", "Worker ready to process tasks")

            # DEBUG: Log initial queue status
            initial_queue_size = self.task_queue.qsize()
            self.logger.info(f"[{worker_id}] Initial queue size: {initial_queue_size}")

            # Main processing loop
            while not shutdown_event.is_set():
                try:
                    # DEBUG: Check queue status
                    queue_size = self.task_queue.qsize()
                    if queue_size > 0:
                        self.logger.info(f"[{worker_id}] Queue has {queue_size} tasks, getting next task...")

                    # Get task from queue
                    task_data = self.task_queue.get(timeout=self.config.queue_timeout)

                    # DEBUG: Log task pickup
                    post_title = task_data.get('post_title', 'Unknown')[:50]
                    self.logger.info(f"[{worker_id}] PICKED UP TASK: {post_title}...")

                    health_monitor.update_thread_health(worker_id, "processing", f"Processing: {task_data['post_title'][:30]}...")

                    # Process the post
                    self._process_post_comments(worker_id, worker_page, task_data)

                    self.task_queue.task_done()
                    health_monitor.update_thread_health(worker_id, "active", "Task completed")

                    self.logger.info(f"[{worker_id}] COMPLETED TASK: {post_title}...")

                except queue.Empty:
                    # No tasks available, continue waiting
                    self.logger.debug(f"[{worker_id}] Queue empty, waiting for tasks...")
                    health_monitor.update_thread_health(worker_id, "waiting", "Waiting for tasks")
                    continue
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing task: {e}")
                    health_monitor.update_thread_health(worker_id, "error", str(e))

                    # Try to recover by recreating the page
                    try:
                        worker_page.close()
                        worker_page = browser_manager.create_tab()
                    except Exception as recovery_error:
                        self.logger.error(f"[{worker_id}] Failed to recover: {recovery_error}")
                        break

                    self.task_queue.task_done()

        except Exception as e:
            self.logger.error(f"[{worker_id}] Critical error in worker thread: {e}")
            health_monitor.update_thread_health(worker_id, "critical_error", str(e))
        finally:
            health_monitor.update_thread_health(worker_id, "stopped", "Worker thread ended")
            self.logger.info(f"[{worker_id}] Worker thread ended")

    def _process_post_comments(self, worker_id: str, worker_page: ChromiumPage, task_data: Dict[str, Any]) -> None:
        """Process comments in a post for flash sales"""
        post_url = task_data["post_url"]
        post_title = task_data["post_title"]
        current_comment_count = task_data["current_comment_count"]
        datetime_attr = task_data["datetime_attr"]

        safe_title = post_title.encode('ascii', 'ignore').decode('ascii')
        self.logger.info(f"[{worker_id}] Processing: {safe_title}")

        try:
            # Check if worker page is still valid
            try:
                current_url = worker_page.url
                if not current_url:
                    self.logger.warning(f"[{worker_id}] Worker page connection lost, recreating...")
                    worker_page = self.browser_manager.create_tab()
                    if not worker_page:
                        self.logger.error(f"[{worker_id}] Failed to recreate worker page")
                        return
            except Exception as e:
                self.logger.warning(f"[{worker_id}] Worker page connection error, recreating: {e}")
                worker_page = self.browser_manager.create_tab()
                if not worker_page:
                    self.logger.error(f"[{worker_id}] Failed to recreate worker page")
                    return

            # Calculate target page directly and navigate there
            target_url = post_url
            if current_comment_count > 30:
                import math
                final_page = math.ceil(current_comment_count / 30)
                base_url = post_url.split('/p')[0]
                target_url = f"{base_url}/p{final_page}"
                self.logger.info(f"[{worker_id}] Navigating directly to final page {final_page}: {target_url}")
            else:
                self.logger.info(f"[{worker_id}] Single page post, navigating to: {target_url}")

            # Navigate directly to target page (first page or calculated final page)
            worker_page.get(target_url)
            worker_page.wait.load_start()

            # Handle Cloudflare verification if present
            if hasattr(self, 'browser_manager'):
                if not self.browser_manager.handle_cloudflare_verification(worker_page, max_wait_time=30):
                    self.logger.warning(f"[{worker_id}] Cloudflare verification failed for {target_url}")
                    return

            # Apply anti-detection delay
            time.sleep(random.uniform(1, 3))

            # Try to navigate to the last page to get newest comments
            # This will handle both paginated and non-paginated posts
            all_comments = self._get_all_comments_with_pagination(worker_page, worker_id, current_comment_count)
            self.logger.info(f"[{worker_id}] Found {len(all_comments)} total comments")

            # Get previous state for incremental processing
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            prev_state = processed_posts.get(post_url, {})
            prev_processed_ids = set(prev_state.get("processed_comment_ids", []))
            last_known_comment_content = prev_state.get("last_comment_content", "")

            new_processed_ids = set()
            flash_sales_found = []

            # For incremental processing: reverse the comments list to start from newest
            # This way we process newest comments first and stop when we reach known content
            comments_to_process = list(reversed(all_comments))

            self.logger.info(f"[{worker_id}] Processing {len(comments_to_process)} comments "
                           f"(incremental: {'yes' if last_known_comment_content else 'no'})")

            # Process each comment (starting from newest)
            processed_new_comments = 0
            for i, comment_element in enumerate(comments_to_process):
                try:
                    comment_id = comment_element.attr('id')

                    comment_text_element = comment_element.ele('.Message')
                    if not comment_text_element:
                        continue

                    comment_text = comment_text_element.text.strip()
                    if len(comment_text) < 10:  # Skip very short comments
                        continue

                    # INCREMENTAL PROCESSING: Stop when we reach the last known comment
                    if last_known_comment_content and comment_text[:200] == last_known_comment_content:
                        self.logger.info(f"[{worker_id}] Reached last known comment, stopping incremental processing")
                        self.logger.info(f"[{worker_id}] Processed {processed_new_comments} new comments")
                        break

                    # Skip if already processed (by ID)
                    if not comment_id or comment_id in prev_processed_ids:
                        continue

                    # Count this as a new comment being processed
                    processed_new_comments += 1
                    new_processed_ids.add(comment_id)

                    # Check for flash sale
                    detection_result = self.detector.is_flash_sale(comment_text)

                    if detection_result["is_flash_sale"]:
                        flash_sale_info = {
                            "post_title": post_title,
                            "post_url": post_url,
                            "comment_id": comment_id,
                            "comment_text": comment_text,
                            "confidence": detection_result["confidence"],
                            "detection_reasons": detection_result["reasons"],
                            "crawled_time": datetime.now(timezone.utc).isoformat(),
                            "worker_id": worker_id
                        }

                        flash_sales_found.append(flash_sale_info)

                        self.logger.info(f"[{worker_id}] Flash sale detected! "
                                       f"Confidence: {detection_result['confidence']:.2f}, "
                                       f"Comment ID: {comment_id}")
                        self.logger.info(f"[{worker_id}] Flash sale content: {comment_text[:100]}...")

                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing comment: {e}")
                    continue

            # Update results and state
            if flash_sales_found:
                with self.results_lock:
                    self.all_flash_sales_found.extend(flash_sales_found)

                    # Immediately save results to file
                    try:
                        self.state_manager.save_results(flash_sales_found)
                        self.logger.info(f"[{worker_id}] Saved {len(flash_sales_found)} flash sales to file")
                    except Exception as save_error:
                        self.logger.error(f"[{worker_id}] Failed to save flash sales: {save_error}")

                self.logger.info(f"[{worker_id}] Found {len(flash_sales_found)} flash sales in this post")

            # Update post state with CORRECT comment count and last comment content
            # For multi-page posts, use the monitor's detected total count, not just current page count
            if current_comment_count > 30:
                # Multi-page post: use monitor's total count
                actual_comment_count = current_comment_count
                self.logger.info(f"[{worker_id}] Multi-page post: using total count {actual_comment_count}")
            else:
                # Single page post: use collected count
                actual_comment_count = len(all_comments)
                self.logger.info(f"[{worker_id}] Single page post: using collected count {actual_comment_count}")

            last_comment_content = ""

            # Get the content of the most recent comment for incremental processing
            if all_comments:
                try:
                    # Get the last (newest) comment's text content
                    last_comment = all_comments[-1]
                    last_comment_content = last_comment.text.strip()[:200]  # Store first 200 chars
                    self.logger.info(f"[{worker_id}] Last comment content: {last_comment_content[:50]}...")
                except Exception as e:
                    self.logger.warning(f"[{worker_id}] Could not extract last comment content: {e}")

            # Update state with correct data
            self._update_processed_post_state(
                post_url,
                actual_comment_count,  # Use correct total count
                datetime_attr,
                prev_processed_ids.union(new_processed_ids),
                last_comment_content  # Add last comment content for incremental processing
            )

            self.logger.info(f"[{worker_id}] Updated state: {post_url[:50]}... "
                           f"Comments: {current_comment_count} -> {actual_comment_count}")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error processing post comments: {e}")
            raise

    def _get_all_comments_with_pagination(self, page: ChromiumPage, worker_id: str, expected_comment_count: int) -> List:
        """Get all comments from current page (we should already be on the correct page)"""
        all_comments = []

        try:
            current_url = page.url
            self.logger.info(f"[{worker_id}] Collecting comments from current page: {current_url}")

            # We should already be on the correct page (either first page or calculated final page)
            # Just get comments from current page
            current_page_comments = self._get_comments_from_current_page(page, worker_id)
            all_comments.extend(current_page_comments)

            self.logger.info(f"[{worker_id}] Got {len(current_page_comments)} comments from current page")

            # If no comments found, try fallback method
            if not all_comments:
                self.logger.warning(f"[{worker_id}] No comments found, trying fallback method...")
                all_comments = self._get_comments_fallback(page, worker_id)

            self.logger.info(f"[{worker_id}] Total comments collected: {len(all_comments)}")
            return all_comments

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error in comment collection: {e}")
            # Always fallback to simple method
            return self._get_comments_fallback(page, worker_id)

    def _navigate_to_last_page(self, page: ChromiumPage, worker_id: str, expected_comment_count: int) -> bool:
        """Navigate to the last page based on LowEndTalk's 30-comments-per-page rule"""
        try:
            original_url = page.url
            self.logger.info(f"[{worker_id}] Calculating pagination for {expected_comment_count} comments")

            # LowEndTalk规律：每页最多30个评论
            # 计算最终页码：comments / 30，向上取整
            if expected_comment_count <= 30:
                self.logger.info(f"[{worker_id}] {expected_comment_count} comments <= 30, no pagination needed")
                return False

            # 计算最终页码
            import math
            final_page = math.ceil(expected_comment_count / 30)
            self.logger.info(f"[{worker_id}] Calculated final page: {final_page} (for {expected_comment_count} comments)")

            # 构造最终页面URL
            base_url = original_url.split('/p')[0]  # 移除现有的页码
            final_page_url = f"{base_url}/p{final_page}"

            self.logger.info(f"[{worker_id}] Navigating directly to final page: {final_page_url}")

            # 直接导航到最终页面
            page.get(final_page_url)
            time.sleep(3)  # 等待页面加载

            # 验证导航是否成功
            new_url = page.url
            if f"/p{final_page}" in new_url:
                self.logger.info(f"[{worker_id}] Successfully navigated to page {final_page}")

                # 验证页面上的评论数量
                comments_on_page = page.eles('.Comment')
                self.logger.info(f"[{worker_id}] Found {len(comments_on_page)} comments on final page")

                return True
            else:
                self.logger.warning(f"[{worker_id}] Navigation failed, current URL: {new_url}")

                # 如果直接导航失败，尝试导航到前一页
                if final_page > 2:
                    fallback_page = final_page - 1
                    fallback_url = f"{base_url}/p{fallback_page}"
                    self.logger.info(f"[{worker_id}] Trying fallback page: {fallback_url}")

                    page.get(fallback_url)
                    time.sleep(3)

                    fallback_new_url = page.url
                    if f"/p{fallback_page}" in fallback_new_url:
                        self.logger.info(f"[{worker_id}] Fallback navigation to page {fallback_page} successful")
                        return True

                return False



        except Exception as e:
            self.logger.error(f"[{worker_id}] Error in pagination navigation: {e}")
            return False

        except Exception as e:
            self.logger.error(f"[{worker_id}] ❌ Error in pagination navigation: {e}")
            return False

    def _get_comments_from_current_page(self, page: ChromiumPage, worker_id: str) -> List:
        """Get all comments from the current page"""
        try:
            # Check if page connection is still valid
            try:
                current_url = page.url
                if not current_url:
                    self.logger.error(f"[{worker_id}] Page connection lost - no URL available")
                    return []
            except Exception as e:
                self.logger.error(f"[{worker_id}] Page connection lost: {e}")
                return []

            # Scroll to load any lazy-loaded content
            self._scroll_to_load_comments(page, worker_id)

            # Get comments using multiple selectors
            comment_selectors = [
                '.Comment',
                '.CommentWrap',
                '.ItemComment',
                '[class*="Comment"]'
            ]

            comments = []
            for selector in comment_selectors:
                try:
                    found_comments = page.eles(selector)
                    if found_comments:
                        comments = found_comments
                        self.logger.debug(f"[{worker_id}] Found {len(comments)} comments using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"[{worker_id}] Comment selector {selector} failed: {e}")
                    continue

            return comments

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error getting comments from current page: {e}")
            return []

    def _get_comments_from_previous_pages(self, page: ChromiumPage, worker_id: str, needed_count: int) -> List:
        """Get comments from previous pages if needed"""
        additional_comments = []

        try:
            # Look for "Previous" or "‹" links to go back
            prev_selectors = [
                'a[title*="Previous"]',
                'a[title*="previous"]',
                'a:contains("Previous")',
                'a:contains("‹")',
                'a:contains("<")'
            ]

            pages_checked = 0
            max_pages_to_check = 3  # Limit to avoid infinite loops

            while len(additional_comments) < needed_count and pages_checked < max_pages_to_check:
                prev_link = None

                for selector in prev_selectors:
                    try:
                        prev_link = page.ele(selector)
                        if prev_link:
                            break
                    except:
                        continue

                if not prev_link:
                    self.logger.debug(f"[{worker_id}] No previous page link found")
                    break

                # Click previous page
                prev_link.click()
                time.sleep(3)
                pages_checked += 1

                # Get comments from this page
                page_comments = self._get_comments_from_current_page(page, worker_id)
                additional_comments.extend(page_comments)

                self.logger.debug(f"[{worker_id}] Got {len(page_comments)} comments from previous page {pages_checked}")

            return additional_comments

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error getting comments from previous pages: {e}")
            return additional_comments

    def _scroll_to_load_comments(self, page: ChromiumPage, worker_id: str) -> None:
        """Scroll page to load all comments on current page"""
        try:
            # Check if page connection is still valid before scrolling
            try:
                current_url = page.url
                if not current_url:
                    self.logger.error(f"[{worker_id}] Cannot scroll - page connection lost")
                    return
            except Exception as e:
                self.logger.error(f"[{worker_id}] Cannot scroll - page connection error: {e}")
                return

            last_height = page.scroll.to_bottom()
            scroll_attempts = 0
            max_scroll_attempts = 5  # Reduced since we're now handling pagination

            while scroll_attempts < max_scroll_attempts:
                time.sleep(self.config.scroll_delay)

                try:
                    new_height = page.scroll.to_bottom()
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Scroll failed - connection lost: {e}")
                    break

                if new_height == last_height:
                    break  # No more content to load

                last_height = new_height
                scroll_attempts += 1

                self.logger.debug(f"[{worker_id}] Scrolled to load more comments (attempt {scroll_attempts})")

            if scroll_attempts >= max_scroll_attempts:
                self.logger.debug(f"[{worker_id}] Reached maximum scroll attempts")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error during scrolling: {e}")

    def _get_comments_fallback(self, page: ChromiumPage, worker_id: str) -> List:
        """Fallback method to get comments if pagination fails"""
        try:
            self.logger.info(f"[{worker_id}] Using fallback method to get comments")
            self._scroll_to_load_comments(page, worker_id)
            comments = page.eles('.Comment')
            return comments
        except Exception as e:
            self.logger.error(f"[{worker_id}] Fallback method also failed: {e}")
            return []

    def _update_processed_post_state(self, post_url: str, comment_count: int,
                                   datetime_attr: str, processed_ids: Set[str],
                                   last_comment_content: str = "") -> None:
        """Update the processed state for a post with actual collected data"""
        try:
            # Use lock to ensure thread-safe state updates
            with self.state_lock:
                current_state = self.state_manager.load_state()
                processed_posts = current_state.get("processed_posts", {})

                # Get previous state for logging
                prev_state = processed_posts.get(post_url, {})
                prev_count = prev_state.get("last_comment_count", 0)

                # Store comprehensive state information
                processed_posts[post_url] = {
                    "last_comment_count": comment_count,
                    "last_comment_datetime": datetime_attr,
                    "processed_comment_ids": list(processed_ids),
                    "last_comment_content": last_comment_content,  # For incremental processing
                    "last_processed_at": datetime.now().isoformat(),  # When this was processed
                    "actual_comments_collected": comment_count  # Actual count from worker
                }

                current_state["processed_posts"] = processed_posts

                # Force save state immediately
                self.state_manager.save_state(current_state)

                # Force file system sync and verify state was saved
                import os
                self.state_manager.state_file_path = os.path.abspath(self.state_manager.state_file_path)

                # Add a small delay to ensure file system write completes
                import time
                time.sleep(0.2)

                # Verify state was saved by reloading from disk
                verification_state = self.state_manager.load_state()
                verification_count = verification_state.get("processed_posts", {}).get(post_url, {}).get("last_comment_count", 0)
                verification_status = verification_state.get("processed_posts", {}).get(post_url, {}).get("status", "unknown")

                if verification_count == comment_count:
                    self.logger.info(f"STATE UPDATED SUCCESSFULLY: {post_url[:50]}... "
                                   f"Comments: {prev_count} -> {comment_count} (verified from disk)")

                    # Remove the "queued" status marker
                    if verification_status == "queued":
                        verification_state["processed_posts"][post_url]["status"] = "processed"
                        self.state_manager.save_state(verification_state)
                        self.logger.info(f"Updated status from 'queued' to 'processed'")

                else:
                    self.logger.error(f"STATE UPDATE FAILED: Expected {comment_count}, verified {verification_count}")
                    self.logger.error(f"State file path: {self.state_manager.state_file_path}")

                    # Try to force save again
                    try:
                        current_state["processed_posts"][post_url]["last_comment_count"] = comment_count
                        current_state["processed_posts"][post_url]["status"] = "force_updated"
                        self.state_manager.save_state(current_state)
                        self.logger.info("Attempted force update of state")
                    except Exception as force_error:
                        self.logger.error(f"Force update also failed: {force_error}")

        except Exception as e:
            self.logger.error(f"Error updating post state: {e}")
            # Try to save state again as fallback
            try:
                import time
                time.sleep(0.5)  # Brief delay

                # Reload and try to save again
                fallback_state = self.state_manager.load_state()
                fallback_posts = fallback_state.get("processed_posts", {})
                fallback_posts[post_url] = {
                    "last_comment_count": comment_count,
                    "last_comment_datetime": datetime_attr,
                    "processed_comment_ids": list(processed_ids),
                    "last_comment_content": last_comment_content,
                    "last_processed_at": datetime.now().isoformat(),
                    "actual_comments_collected": comment_count
                }
                fallback_state["processed_posts"] = fallback_posts
                self.state_manager.save_state(fallback_state)
                self.logger.info("Fallback state save attempted")
            except Exception as fallback_error:
                self.logger.error(f"Fallback state save also failed: {fallback_error}")


def main():
    """Main function to run the improved forum crawler"""
    # Setup logging
    logger = CrawlerLogger.setup_logging("INFO", "forum_crawler.log")

    try:
        # Load configuration
        config = CrawlerConfig.from_file("crawler_config.json")

        logger.info("Starting Improved Forum Crawler")
        logger.info(f"Configuration: {config.num_workers} workers, "
                   f"{config.refresh_interval}s refresh interval")

        # Create and start crawler
        crawler = ForumCrawler(config)
        crawler.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Critical error in main: {e}")
    finally:
        logger.info("Forum crawler shutdown complete")


if __name__ == "__main__":
    main()
