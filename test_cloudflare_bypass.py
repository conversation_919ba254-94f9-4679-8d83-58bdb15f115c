"""
Test script for Cloudflare bypass functionality
This script tests if the browser configuration can successfully bypass Cloudflare verification
"""

import time
import json
import logging
from DrissionPage import ChromiumPage, Chromium, ChromiumOptions
from DrissionPage.common import Settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_cloudflare_config():
    """Load Cloudflare bypass configuration"""
    try:
        with open('cloudflare_bypass_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("cloudflare_bypass_config.json not found")
        return None

def create_stealth_browser(config):
    """Create a browser with stealth configuration"""
    try:
        # Configure DrissionPage settings
        Settings.set_singleton_tab_obj(False)
        
        # Create ChromiumOptions
        options = ChromiumOptions()
        
        # Add all chrome arguments from config
        for arg in config['chrome_arguments']:
            options.add_argument(arg)
        
        # Set user agent
        user_agent = config['anti_detection']['user_agents'][0]
        options.add_argument(f'--user-agent={user_agent}')
        
        # Add experimental options
        exp_options = config['experimental_options']
        options.add_experimental_option("excludeSwitches", exp_options['excludeSwitches'])
        options.add_experimental_option('useAutomationExtension', exp_options['useAutomationExtension'])
        options.add_experimental_option("prefs", exp_options['prefs'])
        
        # Create browser
        browser = Chromium(addr_or_opts=options)
        logger.info("Stealth browser created successfully")
        return browser
        
    except Exception as e:
        logger.error(f"Failed to create stealth browser: {e}")
        return None

def check_cloudflare_verification(page, config):
    """Check if page has Cloudflare verification and wait for completion"""
    try:
        logger.info("Checking for Cloudflare verification...")
        
        # Get Cloudflare indicators from config
        indicators = config['cloudflare_detection']['indicators']
        max_wait_time = config['cloudflare_detection']['max_wait_time']
        check_interval = config['cloudflare_detection']['check_interval']
        
        # Check page title and content
        page_title = page.title if hasattr(page, 'title') else ""
        page_source = ""
        
        try:
            page_source = page.html[:2000]  # First 2000 chars
        except Exception as e:
            logger.warning(f"Could not get page source: {e}")
        
        # Check for Cloudflare indicators
        is_cloudflare = any(indicator.lower() in page_title.lower() for indicator in indicators)
        is_cloudflare = is_cloudflare or any(indicator.lower() in page_source.lower() for indicator in indicators)
        
        if not is_cloudflare:
            logger.info("✅ No Cloudflare verification detected")
            return True
        
        logger.info("🔄 Cloudflare verification detected, waiting for completion...")
        
        # Wait for verification to complete
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            try:
                current_title = page.title if hasattr(page, 'title') else ""
                current_url = page.url if hasattr(page, 'url') else ""
                
                # Check if verification completed
                is_still_cloudflare = any(indicator.lower() in current_title.lower() for indicator in indicators)
                
                if not is_still_cloudflare and 'lowendtalk.com' in current_url:
                    logger.info("✅ Cloudflare verification completed successfully")
                    return True
                
                logger.info(f"⏳ Still waiting... ({int(time.time() - start_time)}s)")
                time.sleep(check_interval)
                
            except Exception as e:
                logger.warning(f"Error during verification check: {e}")
                time.sleep(check_interval)
        
        logger.error(f"❌ Cloudflare verification timed out after {max_wait_time} seconds")
        return False
        
    except Exception as e:
        logger.error(f"Error checking Cloudflare verification: {e}")
        return False

def test_lowendtalk_access():
    """Test access to LowEndTalk forum"""
    config = load_cloudflare_config()
    if not config:
        return False
    
    browser = None
    try:
        # Create stealth browser
        browser = create_stealth_browser(config)
        if not browser:
            return False
        
        # Create a new tab
        page = browser.new_tab()
        
        # Navigate to LowEndTalk
        logger.info("🌐 Navigating to LowEndTalk...")
        page.get("https://lowendtalk.com")
        
        # Wait for initial page load
        time.sleep(3)
        
        # Check for Cloudflare verification
        if not check_cloudflare_verification(page, config):
            logger.error("❌ Failed to bypass Cloudflare verification")
            return False
        
        # Additional wait after verification
        time.sleep(2)
        
        # Check if we successfully reached the forum
        current_title = page.title if hasattr(page, 'title') else ""
        current_url = page.url if hasattr(page, 'url') else ""
        
        logger.info(f"📄 Page title: {current_title}")
        logger.info(f"🔗 Current URL: {current_url}")
        
        # Check for forum indicators
        forum_indicators = ["LowEndTalk", "Discussions", "Forum"]
        is_forum = any(indicator.lower() in current_title.lower() for indicator in forum_indicators)
        
        if is_forum and 'lowendtalk.com' in current_url:
            logger.info("✅ Successfully accessed LowEndTalk forum!")
            
            # Try to find some forum elements
            try:
                discussions = page.eles('.ItemDiscussion')
                logger.info(f"📋 Found {len(discussions)} discussion items")
                
                if discussions:
                    logger.info("✅ Forum content loaded successfully")
                    return True
                else:
                    logger.warning("⚠️ No discussion items found, but page loaded")
                    return True
                    
            except Exception as e:
                logger.warning(f"Could not find forum elements: {e}")
                return True  # Still consider it successful if we reached the page
        else:
            logger.error("❌ Did not reach the expected forum page")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False
    finally:
        if browser:
            try:
                browser.quit()
                logger.info("🔒 Browser closed")
            except Exception as e:
                logger.error(f"Error closing browser: {e}")

def main():
    """Main test function"""
    logger.info("🚀 Starting Cloudflare bypass test...")
    
    success = test_lowendtalk_access()
    
    if success:
        logger.info("🎉 Test completed successfully! Cloudflare bypass is working.")
    else:
        logger.error("💥 Test failed! Cloudflare bypass needs adjustment.")
    
    return success

if __name__ == "__main__":
    main()
