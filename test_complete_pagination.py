"""
Complete test of the pagination functionality
Tests both paginated and non-paginated posts
"""

import time
import logging
from improved_forum_crawler import CrawlerConfig, BrowserManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pagination_logic():
    """Test the complete pagination logic"""
    browser_manager = None
    
    try:
        # Create config
        config = CrawlerConfig(
            enable_pagination=True,
            max_pages_to_check=3,
            comments_per_page=25,
            headless=False  # For debugging
        )
        
        # Create browser manager
        browser_manager = BrowserManager(config)
        browser_manager.start_browser()
        
        # Create a test page
        test_page = browser_manager.create_tab()
        if not test_page:
            logger.error("❌ Failed to create test page")
            return False
        
        # Test URLs - mix of potentially paginated and non-paginated
        test_urls = [
            "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price",
            "https://lowendtalk.com/discussion/137719/lowendtalk-community-rules",
            "https://lowendtalk.com/categories/offers",  # This might have pagination
        ]
        
        success_count = 0
        
        for i, test_url in enumerate(test_urls, 1):
            logger.info(f"\n🧪 Test {i}/{len(test_urls)}: {test_url}")
            
            try:
                # Navigate to the URL
                logger.info(f"🌐 Loading: {test_url}")
                test_page.get(test_url)
                time.sleep(3)
                
                # Handle Cloudflare if present
                if not browser_manager.handle_cloudflare_verification(test_page, max_wait_time=30):
                    logger.warning("⚠️ Cloudflare verification failed, but continuing...")
                
                # Count initial comments
                initial_comments = test_page.eles('.Comment')
                logger.info(f"💬 Initial comments found: {len(initial_comments)}")
                
                # Test the pagination logic
                logger.info("🔍 Testing pagination logic...")
                
                # Simulate the crawler's pagination logic
                worker_id = f"TEST-{i}"
                expected_comment_count = len(initial_comments) + 10  # Simulate detecting more comments
                
                # Test navigation to last page
                last_page_reached = browser_manager._navigate_to_last_page(test_page, worker_id, expected_comment_count)
                
                if last_page_reached:
                    logger.info("✅ Pagination navigation successful")
                    
                    # Count comments on new page
                    new_comments = test_page.eles('.Comment')
                    logger.info(f"💬 Comments after navigation: {len(new_comments)}")
                    
                    # Verify URL changed
                    current_url = test_page.url
                    if current_url != test_url:
                        logger.info(f"✅ URL changed to: {current_url}")
                    else:
                        logger.warning("⚠️ URL didn't change")
                    
                else:
                    logger.info("ℹ️ No pagination found or navigation failed (this is normal for single-page posts)")
                
                # Test comment collection
                logger.info("📝 Testing comment collection...")
                all_comments = browser_manager._get_all_comments_with_pagination(test_page, worker_id, expected_comment_count)
                
                if all_comments:
                    logger.info(f"✅ Collected {len(all_comments)} comments")
                    success_count += 1
                else:
                    logger.warning("⚠️ No comments collected")
                
                # Show some comment details
                for j, comment in enumerate(all_comments[:3]):
                    try:
                        comment_id = comment.attr('id') or f"comment-{j}"
                        comment_text = comment.text.strip()[:50] + "..." if len(comment.text.strip()) > 50 else comment.text.strip()
                        logger.info(f"   💬 Comment {j+1}: {comment_id} - {comment_text}")
                    except:
                        logger.info(f"   💬 Comment {j+1}: [Could not get details]")
                
            except Exception as e:
                logger.error(f"❌ Test {i} failed: {e}")
                continue
        
        # Summary
        logger.info(f"\n📊 Test Results: {success_count}/{len(test_urls)} tests successful")
        
        if success_count == len(test_urls):
            logger.info("🎉 All tests passed! Pagination logic is working correctly.")
        elif success_count > 0:
            logger.info("✅ Some tests passed. Pagination logic is partially working.")
        else:
            logger.error("💥 All tests failed. Pagination logic needs debugging.")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")
        return False
    finally:
        if browser_manager:
            try:
                browser_manager.cleanup()
                logger.info("🔒 Browser closed")
            except:
                pass

def test_crawler_integration():
    """Test pagination within the actual crawler context"""
    logger.info("\n🧪 Testing crawler integration...")
    
    try:
        # Load config
        config = CrawlerConfig.from_file("crawler_config.json")
        config.enable_pagination = True
        config.max_pages_to_check = 2  # Limit for testing
        
        logger.info(f"📋 Config loaded:")
        logger.info(f"   enable_pagination: {config.enable_pagination}")
        logger.info(f"   max_pages_to_check: {config.max_pages_to_check}")
        logger.info(f"   comments_per_page: {config.comments_per_page}")
        
        # Create browser manager
        browser_manager = BrowserManager(config)
        browser_manager.start_browser()
        
        # Test with a simple post
        test_page = browser_manager.create_tab()
        test_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        
        logger.info(f"🌐 Testing crawler integration with: {test_url}")
        test_page.get(test_url)
        time.sleep(3)
        
        # Simulate the worker's comment processing logic
        worker_id = "INTEGRATION-TEST"
        
        # Get initial comment count
        initial_comments = test_page.eles('.Comment')
        current_comment_count = len(initial_comments)
        
        logger.info(f"💬 Current comment count: {current_comment_count}")
        
        # Simulate detecting an increase in comments
        expected_comment_count = current_comment_count + 5
        logger.info(f"📈 Simulating comment increase to: {expected_comment_count}")
        
        # Test the complete pagination workflow
        all_comments = browser_manager._get_all_comments_with_pagination(test_page, worker_id, expected_comment_count)
        
        if all_comments:
            logger.info(f"✅ Integration test successful: {len(all_comments)} comments collected")
            return True
        else:
            logger.error("❌ Integration test failed: No comments collected")
            return False
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False
    finally:
        try:
            browser_manager.cleanup()
        except:
            pass

def main():
    """Main test function"""
    logger.info("🚀 Complete Pagination Test Suite")
    logger.info("=" * 50)
    
    # Test 1: Basic pagination logic
    logger.info("📋 Test 1: Basic pagination logic")
    basic_test = test_pagination_logic()
    
    # Test 2: Crawler integration
    logger.info("\n📋 Test 2: Crawler integration")
    integration_test = test_crawler_integration()
    
    # Final summary
    logger.info("\n📊 Final Results:")
    logger.info(f"   Basic pagination test: {'✅ PASS' if basic_test else '❌ FAIL'}")
    logger.info(f"   Integration test: {'✅ PASS' if integration_test else '❌ FAIL'}")
    
    if basic_test and integration_test:
        logger.info("\n🎉 All tests passed! The pagination functionality is working correctly.")
        logger.info("The crawler should now properly handle both paginated and non-paginated posts.")
    elif basic_test or integration_test:
        logger.info("\n✅ Some tests passed. The pagination functionality is partially working.")
    else:
        logger.error("\n💥 All tests failed. The pagination functionality needs further debugging.")
    
    return basic_test and integration_test

if __name__ == "__main__":
    main()
