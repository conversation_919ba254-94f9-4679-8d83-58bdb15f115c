"""
Test state management and results saving
"""

import time
import logging
import json
import os

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_state_persistence():
    """Test if state is properly saved and loaded"""
    logger.info("Testing state persistence...")
    
    try:
        from improved_forum_crawler import StateManager, CrawlerConfig
        
        config = CrawlerConfig()
        state_manager = StateManager(config)
        
        # Create test state
        test_post_url = "https://lowendtalk.com/discussion/test/123"
        test_state = {
            "processed_posts": {
                test_post_url: {
                    "last_comment_count": 50,
                    "last_comment_datetime": "2024-01-01T12:00:00Z",
                    "processed_comment_ids": ["comment1", "comment2", "comment3"]
                }
            },
            "last_run": "2024-01-01T12:00:00Z"
        }
        
        # Save state
        logger.info("Saving test state...")
        state_manager.save_state(test_state)
        
        # Load state
        logger.info("Loading state...")
        loaded_state = state_manager.load_state()
        
        # Verify
        if test_post_url in loaded_state.get("processed_posts", {}):
            loaded_post = loaded_state["processed_posts"][test_post_url]
            if loaded_post["last_comment_count"] == 50:
                logger.info("State persistence test PASSED")
                return True
            else:
                logger.error(f"Comment count mismatch: expected 50, got {loaded_post['last_comment_count']}")
        else:
            logger.error("Test post not found in loaded state")
        
        return False
        
    except Exception as e:
        logger.error(f"State persistence test failed: {e}")
        return False

def test_results_saving():
    """Test if results are properly saved"""
    logger.info("Testing results saving...")
    
    try:
        from improved_forum_crawler import StateManager, CrawlerConfig
        
        config = CrawlerConfig()
        state_manager = StateManager(config)
        
        # Create test flash sale results
        test_results = [
            {
                "post_url": "https://lowendtalk.com/discussion/test/123",
                "post_title": "Test Flash Sale Post",
                "comment_id": "comment_123",
                "comment_text": "Flash sale: 50% off VPS hosting!",
                "comment_author": "TestUser",
                "comment_datetime": "2024-01-01T12:00:00Z",
                "confidence": 0.95,
                "keywords_found": ["flash sale", "50%", "vps"],
                "detected_at": "2024-01-01T12:00:00Z"
            },
            {
                "post_url": "https://lowendtalk.com/discussion/test/456",
                "post_title": "Another Test Post",
                "comment_id": "comment_456",
                "comment_text": "Limited time offer: cheap servers!",
                "comment_author": "AnotherUser",
                "comment_datetime": "2024-01-01T13:00:00Z",
                "confidence": 0.88,
                "keywords_found": ["limited time", "cheap"],
                "detected_at": "2024-01-01T13:00:00Z"
            }
        ]
        
        # Save results
        logger.info("Saving test results...")
        state_manager.save_results(test_results)
        
        # Load results
        logger.info("Loading results...")
        loaded_results = state_manager.load_results()
        
        # Verify
        if len(loaded_results) >= len(test_results):
            # Check if our test results are in the loaded results
            found_count = 0
            for test_result in test_results:
                for loaded_result in loaded_results:
                    if (loaded_result.get("comment_id") == test_result["comment_id"] and
                        loaded_result.get("post_url") == test_result["post_url"]):
                        found_count += 1
                        break
            
            if found_count == len(test_results):
                logger.info("Results saving test PASSED")
                return True
            else:
                logger.error(f"Only found {found_count}/{len(test_results)} test results")
        else:
            logger.error(f"Expected at least {len(test_results)} results, got {len(loaded_results)}")
        
        return False
        
    except Exception as e:
        logger.error(f"Results saving test failed: {e}")
        return False

def test_state_update_race_condition():
    """Test if state updates work correctly with multiple threads"""
    logger.info("Testing state update race conditions...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        import threading
        import time
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Simulate multiple workers updating state
        test_post_url = "https://lowendtalk.com/discussion/race/test"
        
        def worker_update(worker_id, comment_count):
            try:
                crawler._update_processed_post_state(
                    test_post_url, 
                    comment_count, 
                    "2024-01-01T12:00:00Z", 
                    {f"comment_{worker_id}_{i}" for i in range(5)}
                )
                logger.info(f"Worker {worker_id} updated state with {comment_count} comments")
            except Exception as e:
                logger.error(f"Worker {worker_id} failed: {e}")
        
        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker_update, args=(i, 10 + i))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Check final state
        final_state = crawler.state_manager.load_state()
        if test_post_url in final_state.get("processed_posts", {}):
            final_count = final_state["processed_posts"][test_post_url]["last_comment_count"]
            logger.info(f"Final comment count: {final_count}")
            logger.info("Race condition test PASSED")
            return True
        else:
            logger.error("Test post not found in final state")
            return False
        
    except Exception as e:
        logger.error(f"Race condition test failed: {e}")
        return False

def check_current_state():
    """Check the current state file"""
    logger.info("Checking current state file...")
    
    try:
        state_file = "lowendtalk_crawl_state.json"
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            processed_posts = state.get("processed_posts", {})
            logger.info(f"Current state has {len(processed_posts)} processed posts")
            
            # Show some examples
            for i, (url, data) in enumerate(list(processed_posts.items())[:3]):
                logger.info(f"  Post {i+1}: {url[:50]}... (Comments: {data.get('last_comment_count', 0)})")
            
            return True
        else:
            logger.warning("State file does not exist")
            return False
            
    except Exception as e:
        logger.error(f"Failed to check current state: {e}")
        return False

def check_current_results():
    """Check the current results file"""
    logger.info("Checking current results file...")
    
    try:
        results_file = "flash_sales.json"
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            logger.info(f"Current results file has {len(results)} flash sale entries")
            
            # Show recent results
            for i, result in enumerate(results[-3:]):
                logger.info(f"  Result {i+1}: {result.get('post_title', 'Unknown')[:50]}... "
                           f"(Confidence: {result.get('confidence', 0):.2f})")
            
            return True
        else:
            logger.warning("Results file does not exist")
            return False
            
    except Exception as e:
        logger.error(f"Failed to check current results: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing State Management and Results Saving")
    logger.info("=" * 50)
    
    # Check current files
    logger.info("Step 1: Checking current files")
    check_current_state()
    check_current_results()
    
    # Test state persistence
    logger.info("\nStep 2: Testing state persistence")
    state_test = test_state_persistence()
    
    # Test results saving
    logger.info("\nStep 3: Testing results saving")
    results_test = test_results_saving()
    
    # Test race conditions
    logger.info("\nStep 4: Testing race conditions")
    race_test = test_state_update_race_condition()
    
    # Summary
    logger.info("\nTest Results:")
    logger.info(f"State persistence: {'PASS' if state_test else 'FAIL'}")
    logger.info(f"Results saving: {'PASS' if results_test else 'FAIL'}")
    logger.info(f"Race conditions: {'PASS' if race_test else 'FAIL'}")
    
    total_passed = sum([state_test, results_test, race_test])
    
    if total_passed == 3:
        logger.info("\nAll tests PASSED! State and results management is working correctly.")
    elif total_passed > 0:
        logger.info(f"\n{total_passed}/3 tests passed. Some issues may remain.")
    else:
        logger.error("\nAll tests FAILED! State and results management needs debugging.")
    
    return total_passed > 0

if __name__ == "__main__":
    main()
