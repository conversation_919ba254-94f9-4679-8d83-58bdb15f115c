"""
Automated pagination debugging script - no user input required
This script will automatically test pagination functionality
"""

import time
import logging
import re
from DrissionPage import ChromiumPage, Chromium, ChromiumOptions
from DrissionPage.common import Settings

# Setup detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_browser():
    """Create a browser for testing"""
    try:
        Settings.set_singleton_tab_obj(False)
        
        options = ChromiumOptions()
        
        # Cloudflare bypass settings
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        options.add_argument(f'--user-agent={user_agent}')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        browser = Chromium(addr_or_opts=options)
        logger.info("✅ Test browser created")
        return browser
        
    except Exception as e:
        logger.error(f"❌ Failed to create test browser: {e}")
        return None

def wait_for_cloudflare_bypass(page, max_wait=30):
    """Wait for Cloudflare verification to complete"""
    logger.info("🔍 Checking for Cloudflare verification...")
    
    cloudflare_indicators = [
        'checking your browser',
        'ddos protection',
        'please wait',
        'cloudflare',
        'just a moment',
        'verifying you are human'
    ]
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            current_title = page.title.lower() if hasattr(page, 'title') else ""
            current_url = page.url if hasattr(page, 'url') else ""
            
            is_cloudflare = any(indicator in current_title for indicator in cloudflare_indicators)
            
            if not is_cloudflare and 'lowendtalk.com' in current_url:
                logger.info("✅ Successfully bypassed Cloudflare verification!")
                return True
            
            if is_cloudflare:
                elapsed = int(time.time() - start_time)
                logger.info(f"⏳ Cloudflare verification in progress... ({elapsed}s)")
            
            time.sleep(2)
            
        except Exception as e:
            logger.warning(f"Error checking page status: {e}")
            time.sleep(2)
    
    logger.error(f"❌ Cloudflare verification timed out after {max_wait} seconds")
    return False

def find_multi_page_post():
    """Find a post with multiple pages by checking the offers section"""
    browser = None
    
    try:
        browser = create_test_browser()
        if not browser:
            return None
        
        page = browser.new_tab()
        
        logger.info("🔍 Looking for multi-page posts in LowEndTalk offers...")
        page.get("https://lowendtalk.com/categories/offers")
        
        if not wait_for_cloudflare_bypass(page):
            logger.error("❌ Failed to bypass Cloudflare on offers page")
            return None
        
        # Find posts with high comment counts
        posts = page.eles('.ItemDiscussion')
        if not posts:
            posts = page.eles('.Item')
        
        multi_page_post = None
        
        for post in posts[:10]:  # Check first 10 posts
            try:
                # Look for comment count
                comment_count_elem = post.ele('.MItem.MCount')
                if comment_count_elem:
                    comment_text = comment_count_elem.text.strip()
                    # Extract number from text like "123 comments"
                    import re
                    count_match = re.search(r'(\d+)', comment_text)
                    if count_match:
                        comment_count = int(count_match.group(1))
                        if comment_count > 50:  # Likely to have multiple pages
                            title_link = post.ele('a')
                            if title_link:
                                post_url = title_link.link
                                post_title = title_link.text.strip()
                                logger.info(f"📄 Found potential multi-page post: {post_title} ({comment_count} comments)")
                                logger.info(f"🔗 URL: {post_url}")
                                multi_page_post = post_url
                                break
            except Exception as e:
                logger.debug(f"Error checking post: {e}")
                continue
        
        return multi_page_post
        
    except Exception as e:
        logger.error(f"❌ Error finding multi-page post: {e}")
        return None
    finally:
        if browser:
            try:
                browser.quit()
            except:
                pass

def analyze_pagination_elements(page):
    """Analyze all potential pagination elements on the page"""
    logger.info("🔍 Analyzing pagination elements...")
    
    # Check all possible selectors
    selectors_to_test = [
        'a[href*="/p"]',
        '.Pager a',
        '.PageNavigation a',
        'a[href*="page="]',
        'a[href*="/p2"]',
        'a[href*="/p3"]',
        'a[href*="/p4"]',
        'a[href*="/p5"]',
        'a:contains("2")',
        'a:contains("3")',
        'a:contains("Next")',
        'a:contains("»")',
        'a:contains("Last")',
    ]
    
    found_elements = {}
    
    for selector in selectors_to_test:
        try:
            elements = page.eles(selector)
            if elements:
                found_elements[selector] = []
                for elem in elements[:5]:  # Show first 5
                    try:
                        text = elem.text.strip()
                        href = elem.attr('href') or ''
                        found_elements[selector].append({
                            'text': text,
                            'href': href
                        })
                    except:
                        continue
        except Exception as e:
            logger.debug(f"Selector {selector} failed: {e}")
    
    # Report findings
    if found_elements:
        logger.info("✅ Found pagination elements:")
        for selector, elements in found_elements.items():
            logger.info(f"  📄 {selector}: {len(elements)} elements")
            for elem in elements:
                logger.info(f"    - '{elem['text']}' -> {elem['href']}")
    else:
        logger.warning("⚠️ No pagination elements found with any selector")
    
    return found_elements

def test_pagination_navigation(page, found_elements):
    """Test actual navigation using found pagination elements"""
    logger.info("🧪 Testing pagination navigation...")
    
    original_url = page.url
    logger.info(f"📍 Original URL: {original_url}")
    
    # Try each type of pagination element
    for selector, elements in found_elements.items():
        if not elements:
            continue
            
        logger.info(f"🔄 Testing navigation with selector: {selector}")
        
        for elem_info in elements:
            try:
                text = elem_info['text']
                href = elem_info['href']
                
                # Skip if it's not a page number or navigation link
                if not (text.isdigit() or text in ['Next', '»', 'Last', '>']):
                    continue
                
                logger.info(f"🔗 Attempting to click: '{text}' -> {href}")
                
                # Find the actual element again (it might have changed)
                actual_elements = page.eles(selector)
                target_element = None
                
                for actual_elem in actual_elements:
                    if actual_elem.text.strip() == text and actual_elem.attr('href') == href:
                        target_element = actual_elem
                        break
                
                if target_element:
                    # Click the element
                    target_element.click()
                    time.sleep(3)
                    
                    new_url = page.url
                    logger.info(f"📍 New URL: {new_url}")
                    
                    if new_url != original_url:
                        logger.info("✅ Navigation successful!")
                        
                        # Count comments on new page
                        comments = page.eles('.Comment')
                        logger.info(f"💬 Comments on new page: {len(comments)}")
                        
                        return True
                    else:
                        logger.warning("⚠️ URL didn't change")
                else:
                    logger.warning(f"⚠️ Could not find element to click: '{text}'")
                    
            except Exception as e:
                logger.error(f"❌ Error clicking element '{text}': {e}")
                continue
    
    logger.error("❌ No successful navigation achieved")
    return False

def test_specific_post(post_url):
    """Test pagination on a specific post"""
    browser = None
    
    try:
        browser = create_test_browser()
        if not browser:
            return False
        
        page = browser.new_tab()
        
        logger.info(f"🌐 Testing pagination on: {post_url}")
        page.get(post_url)
        
        if not wait_for_cloudflare_bypass(page):
            logger.error("❌ Failed to bypass Cloudflare")
            return False
        
        # Count initial comments
        initial_comments = page.eles('.Comment')
        logger.info(f"📝 Initial comments: {len(initial_comments)}")
        
        # Analyze pagination elements
        found_elements = analyze_pagination_elements(page)
        
        if not found_elements:
            logger.warning("⚠️ No pagination elements found - post may have only one page")
            return False
        
        # Test navigation
        navigation_success = test_pagination_navigation(page, found_elements)
        
        return navigation_success
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if browser:
            try:
                browser.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def main():
    """Main test function"""
    logger.info("🚀 Automated Pagination Debug")
    logger.info("=" * 40)
    
    # First, try to find a multi-page post automatically
    logger.info("🔍 Step 1: Finding a multi-page post...")
    multi_page_url = find_multi_page_post()
    
    if multi_page_url:
        logger.info(f"✅ Found multi-page post: {multi_page_url}")
        
        # Test pagination on this post
        logger.info("🧪 Step 2: Testing pagination...")
        success = test_specific_post(multi_page_url)
        
        if success:
            logger.info("🎉 Pagination test PASSED!")
        else:
            logger.error("💥 Pagination test FAILED!")
        
        return success
    else:
        logger.error("❌ Could not find a suitable multi-page post to test")
        
        # Test with a known URL as fallback
        fallback_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        logger.info(f"🔄 Fallback: Testing with known URL: {fallback_url}")
        
        return test_specific_post(fallback_url)

if __name__ == "__main__":
    main()
