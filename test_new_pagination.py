"""
Test the new pagination logic based on 30 comments per page
"""

import time
import logging
import sys
import math

# Setup logging without emoji
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pagination_calculation():
    """Test the pagination calculation logic"""
    logger.info("Testing pagination calculation logic...")
    
    test_cases = [
        (25, False, "25 comments - no pagination needed"),
        (30, False, "30 comments - exactly one page"),
        (31, 2, "31 comments - should go to page 2"),
        (45, 2, "45 comments - should go to page 2"),
        (60, 2, "60 comments - exactly 2 pages"),
        (61, 3, "61 comments - should go to page 3"),
        (90, 3, "90 comments - exactly 3 pages"),
        (100, 4, "100 comments - should go to page 4"),
    ]
    
    for comment_count, expected_page, description in test_cases:
        if comment_count <= 30:
            calculated_page = False
        else:
            calculated_page = math.ceil(comment_count / 30)
        
        if calculated_page == expected_page:
            logger.info(f"PASS: {description} -> {calculated_page}")
        else:
            logger.error(f"FAIL: {description} -> Expected {expected_page}, got {calculated_page}")
    
    logger.info("Pagination calculation test completed")

def test_url_construction():
    """Test URL construction for pagination"""
    logger.info("Testing URL construction...")
    
    base_urls = [
        "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price",
        "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price/p2",
        "https://lowendtalk.com/discussion/123456/test-post/p5",
    ]
    
    for base_url in base_urls:
        # Remove existing page number
        clean_url = base_url.split('/p')[0]
        
        # Test different page numbers
        for page_num in [2, 3, 4]:
            final_url = f"{clean_url}/p{page_num}"
            logger.info(f"Base: {base_url}")
            logger.info(f"Clean: {clean_url}")
            logger.info(f"Final: {final_url}")
            logger.info("---")
    
    logger.info("URL construction test completed")

def test_real_pagination():
    """Test real pagination with browser"""
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler, BrowserManager
        
        logger.info("Testing real pagination...")
        
        config = CrawlerConfig(
            enable_pagination=True,
            comments_per_page=30,  # Updated to 30
            headless=False
        )
        
        crawler = ForumCrawler(config)
        browser_manager = BrowserManager(config)
        browser_manager.start_browser()
        
        test_page = browser_manager.create_tab()
        if not test_page:
            logger.error("Failed to create test page")
            return False
        
        # Test with a post
        test_url = "https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price"
        logger.info(f"Testing with: {test_url}")
        
        test_page.get(test_url)
        time.sleep(3)
        
        # Handle Cloudflare
        browser_manager.handle_cloudflare_verification(test_page, max_wait_time=20)
        
        # Count comments
        comments = test_page.eles('.Comment')
        comment_count = len(comments)
        logger.info(f"Found {comment_count} comments on current page")
        
        # Test pagination logic with different simulated counts
        test_counts = [25, 35, 65, 95]
        
        for simulated_count in test_counts:
            logger.info(f"\nTesting with simulated count: {simulated_count}")
            
            # Reset to original page
            test_page.get(test_url)
            time.sleep(2)
            
            # Test navigation
            worker_id = f"TEST-{simulated_count}"
            success = crawler._navigate_to_last_page(test_page, worker_id, simulated_count)
            
            if success:
                logger.info(f"Navigation successful for {simulated_count} comments")
                
                # Check current URL
                current_url = test_page.url
                logger.info(f"Current URL: {current_url}")
                
                # Count comments on this page
                page_comments = test_page.eles('.Comment')
                logger.info(f"Comments on this page: {len(page_comments)}")
            else:
                logger.info(f"No navigation needed for {simulated_count} comments (single page)")
        
        browser_manager.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"Real pagination test failed: {e}")
        return False

def test_state_management():
    """Test state management to prevent duplicate processing"""
    logger.info("Testing state management...")
    
    try:
        from improved_forum_crawler import StateManager, CrawlerConfig
        
        config = CrawlerConfig()
        state_manager = StateManager(config)
        
        # Create test state
        test_state = {
            "processed_posts": {
                "https://lowendtalk.com/discussion/123/test": {
                    "last_comment_count": 25,
                    "last_comment_datetime": "2024-01-01T12:00:00Z",
                    "processed_comment_ids": ["comment1", "comment2"]
                }
            },
            "last_run": "2024-01-01T12:00:00Z"
        }
        
        # Save and load state
        state_manager.save_state(test_state)
        loaded_state = state_manager.load_state()
        
        if loaded_state["processed_posts"] == test_state["processed_posts"]:
            logger.info("State management test PASSED")
            return True
        else:
            logger.error("State management test FAILED")
            return False
            
    except Exception as e:
        logger.error(f"State management test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing New Pagination Logic")
    logger.info("=" * 50)
    
    # Test 1: Calculation logic
    logger.info("Test 1: Pagination calculation")
    test_pagination_calculation()
    
    # Test 2: URL construction
    logger.info("\nTest 2: URL construction")
    test_url_construction()
    
    # Test 3: State management
    logger.info("\nTest 3: State management")
    state_test = test_state_management()
    
    # Test 4: Real pagination
    logger.info("\nTest 4: Real pagination")
    real_test = test_real_pagination()
    
    # Summary
    logger.info("\nTest Results:")
    logger.info(f"State management: {'PASS' if state_test else 'FAIL'}")
    logger.info(f"Real pagination: {'PASS' if real_test else 'FAIL'}")
    
    if state_test and real_test:
        logger.info("\nAll tests PASSED! New pagination logic is working.")
    else:
        logger.error("\nSome tests FAILED. Check the implementation.")
    
    return state_test and real_test

if __name__ == "__main__":
    main()
