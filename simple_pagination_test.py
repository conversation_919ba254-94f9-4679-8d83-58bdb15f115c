"""
Simple test to verify pagination functionality works
"""

import time
import logging
from DrissionPage import ChromiumPage, Chromium, ChromiumOptions
from DrissionPage.common import Settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_browser():
    """Create a browser for testing"""
    try:
        Settings.set_singleton_tab_obj(False)
        
        options = ChromiumOptions()
        
        # Basic stealth settings for Cloudflare bypass
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
        options.add_argument(f'--user-agent={user_agent}')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        browser = Chromium(addr_or_opts=options)
        logger.info("✅ Test browser created")
        return browser
        
    except Exception as e:
        logger.error(f"❌ Failed to create test browser: {e}")
        return None

def test_pagination_detection():
    """Test if we can detect and navigate pagination"""
    browser = None
    
    try:
        browser = create_test_browser()
        if not browser:
            return False
        
        page = browser.new_tab()
        
        # Test with a known multi-page post (you may need to update this URL)
        test_url = input("Enter a LowEndTalk post URL with multiple pages (or press Enter to skip): ").strip()
        
        if not test_url:
            logger.info("ℹ️ No URL provided, testing pagination detection logic only")
            return True
        
        logger.info(f"🌐 Testing pagination on: {test_url}")
        page.get(test_url)
        time.sleep(3)
        
        # Check for Cloudflare
        title = page.title.lower() if hasattr(page, 'title') else ""
        if 'cloudflare' in title or 'checking' in title:
            logger.info("⏳ Waiting for Cloudflare verification...")
            time.sleep(10)
        
        # Test pagination detection
        logger.info("🔍 Looking for pagination elements...")
        
        pagination_selectors = [
            '.Pager a',
            '.PageNavigation a', 
            '.pager a',
            'a[href*="p2"]',
            'a[href*="page"]'
        ]
        
        pagination_found = False
        for selector in pagination_selectors:
            try:
                page_links = page.eles(selector)
                if page_links:
                    logger.info(f"✅ Found {len(page_links)} pagination links with selector: {selector}")
                    
                    # Show page numbers
                    page_numbers = []
                    for link in page_links[:5]:  # Show first 5
                        text = link.text.strip()
                        href = link.attr('href') or ''
                        logger.info(f"   📄 Link: '{text}' -> {href}")
                        if text.isdigit():
                            page_numbers.append(int(text))
                    
                    if page_numbers:
                        max_page = max(page_numbers)
                        logger.info(f"📊 Detected pages up to: {max_page}")
                        pagination_found = True
                        
                        # Try to navigate to last page
                        if max_page > 1:
                            logger.info(f"🔄 Attempting to navigate to page {max_page}")
                            for link in page_links:
                                if link.text.strip() == str(max_page):
                                    initial_url = page.url
                                    link.click()
                                    time.sleep(3)
                                    new_url = page.url
                                    
                                    if new_url != initial_url:
                                        logger.info("✅ Successfully navigated to different page")
                                        
                                        # Count comments on this page
                                        comments = page.eles('.Comment')
                                        logger.info(f"📝 Found {len(comments)} comments on this page")
                                        
                                        return True
                                    else:
                                        logger.warning("⚠️ URL didn't change after clicking")
                                    break
                    break
                    
            except Exception as e:
                logger.debug(f"Selector {selector} failed: {e}")
                continue
        
        if not pagination_found:
            logger.info("ℹ️ No pagination found - this post may have only one page")
            
            # Still count comments on single page
            comments = page.eles('.Comment')
            logger.info(f"📝 Found {len(comments)} comments on single page")
            
            return True
        
        return pagination_found
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if browser:
            try:
                browser.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def test_comment_collection():
    """Test comment collection functionality"""
    logger.info("\n📝 Testing comment collection...")
    
    browser = None
    try:
        browser = create_test_browser()
        if not browser:
            return False
        
        page = browser.new_tab()
        
        # Go to LowEndTalk offers page
        logger.info("🌐 Loading LowEndTalk offers page...")
        page.get("https://lowendtalk.com/categories/offers")
        time.sleep(3)
        
        # Find first post
        posts = page.eles('.ItemDiscussion')
        if not posts:
            posts = page.eles('.Item')
        
        if posts:
            first_post = posts[0]
            title_link = first_post.ele('a')
            if title_link:
                post_title = title_link.text.strip()
                post_url = title_link.link
                
                logger.info(f"📄 Testing with post: {post_title[:50]}...")
                logger.info(f"🔗 URL: {post_url}")
                
                # Navigate to post
                page.get(post_url)
                time.sleep(3)
                
                # Count comments
                comments = page.eles('.Comment')
                logger.info(f"📝 Found {len(comments)} comments")
                
                if comments:
                    # Show first few comment IDs
                    for i, comment in enumerate(comments[:3]):
                        comment_id = comment.attr('id')
                        logger.info(f"   💬 Comment {i+1} ID: {comment_id}")
                
                return True
            else:
                logger.error("❌ Could not find post title link")
                return False
        else:
            logger.error("❌ Could not find any posts")
            return False
            
    except Exception as e:
        logger.error(f"❌ Comment collection test failed: {e}")
        return False
    finally:
        if browser:
            try:
                browser.quit()
            except:
                pass

def main():
    """Main test function"""
    logger.info("🚀 Simple Pagination Test")
    logger.info("=" * 40)
    
    # Test comment collection first
    comment_test = test_comment_collection()
    
    # Test pagination detection
    pagination_test = test_pagination_detection()
    
    # Summary
    logger.info("\n📊 Test Results:")
    logger.info(f"   Comment Collection: {'✅ PASS' if comment_test else '❌ FAIL'}")
    logger.info(f"   Pagination Detection: {'✅ PASS' if pagination_test else '❌ FAIL'}")
    
    if comment_test and pagination_test:
        logger.info("\n🎉 Pagination functionality appears to be working!")
        logger.info("💡 Tips for testing:")
        logger.info("   1. Find a post with many comments (100+ comments)")
        logger.info("   2. Run this test with that URL")
        logger.info("   3. Verify it can navigate to different pages")
    else:
        logger.error("\n💥 Some tests failed. Check the implementation.")
    
    return comment_test and pagination_test

if __name__ == "__main__":
    main()
