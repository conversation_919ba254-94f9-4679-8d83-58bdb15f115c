"""
Test the state management fixes
"""

import time
import logging
import json
import os
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_current_state():
    """Check current state file"""
    logger.info("Checking current state file...")
    
    try:
        state_file = "lowendtalk_crawl_state.json"
        if os.path.exists(state_file):
            with open(state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            processed_posts = state.get("processed_posts", {})
            logger.info(f"Current state has {len(processed_posts)} processed posts")
            
            # Look for the birthday post specifically
            birthday_post = None
            for url, data in processed_posts.items():
                if "15th-birthday" in url.lower() or "birthday" in url.lower():
                    birthday_post = (url, data)
                    break
            
            if birthday_post:
                url, data = birthday_post
                comment_count = data.get("last_comment_count", 0)
                last_content = data.get("last_comment_content", "")[:50]
                last_processed = data.get("last_processed_at", "unknown")
                
                logger.info(f"Birthday post found:")
                logger.info(f"  URL: {url}")
                logger.info(f"  Comment count: {comment_count}")
                logger.info(f"  Last content: {last_content}...")
                logger.info(f"  Last processed: {last_processed}")
                
                return comment_count
            else:
                logger.warning("Birthday post not found in state")
                return None
        else:
            logger.warning("State file does not exist")
            return None
            
    except Exception as e:
        logger.error(f"Failed to check current state: {e}")
        return None

def test_state_lock():
    """Test if state_lock is properly initialized"""
    logger.info("Testing state_lock initialization...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        if hasattr(crawler, 'state_lock'):
            logger.info("✅ state_lock is properly initialized")
            
            # Test the lock
            with crawler.state_lock:
                logger.info("✅ state_lock can be acquired and released")
            
            return True
        else:
            logger.error("❌ state_lock is missing!")
            return False
            
    except Exception as e:
        logger.error(f"State lock test failed: {e}")
        return False

def test_comment_count_logic():
    """Test comment count calculation logic"""
    logger.info("Testing comment count calculation logic...")
    
    test_cases = [
        # (current_comment_count, all_comments_length, expected_result, description)
        (752, 20, 752, "Multi-page post: should use total count"),
        (25, 25, 25, "Single page post: should use collected count"),
        (100, 30, 100, "Multi-page post: should use total count"),
        (15, 15, 15, "Single page post: should use collected count"),
    ]
    
    for current_count, collected_count, expected, description in test_cases:
        if current_count > 30:
            # Multi-page post: use monitor's total count
            actual_result = current_count
        else:
            # Single page post: use collected count
            actual_result = collected_count
        
        if actual_result == expected:
            logger.info(f"✅ {description}: {actual_result}")
        else:
            logger.error(f"❌ {description}: Expected {expected}, got {actual_result}")
    
    return True

def test_state_update():
    """Test state update mechanism"""
    logger.info("Testing state update mechanism...")
    
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler
        
        config = CrawlerConfig()
        crawler = ForumCrawler(config)
        
        # Test state update
        test_url = "https://lowendtalk.com/discussion/test/state-fix"
        test_comment_count = 752  # Simulate the birthday post
        test_datetime = datetime.now().isoformat()
        test_processed_ids = {"comment_1", "comment_2", "comment_3"}
        test_last_content = "This is the last comment content for testing."
        
        logger.info(f"Testing state update with:")
        logger.info(f"  URL: {test_url}")
        logger.info(f"  Comment count: {test_comment_count}")
        logger.info(f"  Last content: {test_last_content[:50]}...")
        
        # Update state
        crawler._update_processed_post_state(
            test_url,
            test_comment_count,
            test_datetime,
            test_processed_ids,
            test_last_content
        )
        
        # Verify state was updated
        time.sleep(1)
        current_state = crawler.state_manager.load_state()
        processed_posts = current_state.get("processed_posts", {})
        
        if test_url in processed_posts:
            saved_data = processed_posts[test_url]
            saved_count = saved_data.get("last_comment_count", 0)
            saved_content = saved_data.get("last_comment_content", "")
            
            if saved_count == test_comment_count and saved_content == test_last_content:
                logger.info("✅ State update test PASSED!")
                logger.info(f"  Verified comment count: {saved_count}")
                logger.info(f"  Verified last content: {saved_content[:50]}...")
                return True
            else:
                logger.error("❌ State update test FAILED!")
                logger.error(f"  Expected count: {test_comment_count}, got: {saved_count}")
                logger.error(f"  Expected content: {test_last_content[:50]}...")
                logger.error(f"  Got content: {saved_content[:50]}...")
                return False
        else:
            logger.error("❌ State update test FAILED! Test URL not found in state")
            return False
            
    except Exception as e:
        logger.error(f"State update test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing State Management Fixes")
    logger.info("=" * 50)
    
    # Check current state
    logger.info("Step 1: Checking current state")
    current_count = check_current_state()
    
    # Test state lock
    logger.info("\nStep 2: Testing state lock")
    lock_test = test_state_lock()
    
    # Test comment count logic
    logger.info("\nStep 3: Testing comment count logic")
    count_test = test_comment_count_logic()
    
    # Test state update
    logger.info("\nStep 4: Testing state update")
    update_test = test_state_update()
    
    # Summary
    logger.info("\nTEST RESULTS:")
    logger.info("=" * 30)
    logger.info(f"State lock: {'PASS' if lock_test else 'FAIL'}")
    logger.info(f"Comment count logic: {'PASS' if count_test else 'FAIL'}")
    logger.info(f"State update: {'PASS' if update_test else 'FAIL'}")
    
    if current_count:
        logger.info(f"Current birthday post count: {current_count}")
    
    total_passed = sum([lock_test, count_test, update_test])
    
    if total_passed == 3:
        logger.info("\n✅ ALL TESTS PASSED!")
        logger.info("State management fixes are working correctly.")
        logger.info("The crawler should now:")
        logger.info("  - Have proper state_lock initialization")
        logger.info("  - Use correct comment counts (752, not 2)")
        logger.info("  - Successfully save state to JSON file")
    elif total_passed > 0:
        logger.info(f"\n⚠️ {total_passed}/3 tests passed.")
        logger.info("Some fixes are working, but issues remain.")
    else:
        logger.error("\n❌ ALL TESTS FAILED!")
        logger.error("State management fixes need further debugging.")
    
    return total_passed == 3

if __name__ == "__main__":
    main()
