"""
Quick Cloudflare Bypass Test
A simple script to quickly test if the browser configuration can bypass Cloudflare
"""

import time
import logging
from DrissionPage import ChromiumPage, Chromium, ChromiumOptions
from DrissionPage.common import Settings

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_stealth_browser():
    """Create a browser with enhanced stealth configuration"""
    try:
        # Configure DrissionPage settings
        Settings.set_singleton_tab_obj(False)
        
        # Create ChromiumOptions
        options = ChromiumOptions()
        
        # Essential Cloudflare bypass arguments
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-component-extensions-with-background-pages')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-hang-monitor')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-domain-reliability')
        options.add_argument('--disable-component-update')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--start-maximized')
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-notifications')
        
        # Set realistic user agent
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
        options.add_argument(f'--user-agent={user_agent}')
        
        # Critical stealth settings
        options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Additional prefs
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,
                "geolocation": 2,
            },
            "profile.managed_default_content_settings": {
                "images": 1
            }
        }
        options.add_experimental_option("prefs", prefs)
        
        # Create browser
        browser = Chromium(addr_or_opts=options)
        logger.info("✅ Stealth browser created successfully")
        return browser
        
    except Exception as e:
        logger.error(f"❌ Failed to create stealth browser: {e}")
        return None

def wait_for_cloudflare_bypass(page, max_wait=60):
    """Wait for Cloudflare verification to complete"""
    logger.info("🔍 Checking for Cloudflare verification...")
    
    cloudflare_indicators = [
        'checking your browser',
        'ddos protection',
        'please wait',
        'cloudflare',
        'just a moment',
        'verifying you are human',
        'cf-browser-verification',
        'cf-challenge-running'
    ]
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            # Get current page info
            current_title = page.title.lower() if hasattr(page, 'title') else ""
            current_url = page.url if hasattr(page, 'url') else ""
            
            # Check if we're still on Cloudflare verification
            is_cloudflare = any(indicator in current_title for indicator in cloudflare_indicators)
            
            if not is_cloudflare and 'lowendtalk.com' in current_url:
                logger.info("✅ Successfully bypassed Cloudflare verification!")
                return True
            
            if is_cloudflare:
                elapsed = int(time.time() - start_time)
                logger.info(f"⏳ Cloudflare verification in progress... ({elapsed}s)")
            
            time.sleep(2)
            
        except Exception as e:
            logger.warning(f"Error checking page status: {e}")
            time.sleep(2)
    
    logger.error(f"❌ Cloudflare verification timed out after {max_wait} seconds")
    return False

def test_lowendtalk():
    """Test access to LowEndTalk"""
    browser = None
    
    try:
        # Create browser
        browser = create_stealth_browser()
        if not browser:
            return False
        
        # Create page
        page = browser.new_tab()
        
        # Navigate to LowEndTalk
        logger.info("🌐 Navigating to LowEndTalk...")
        page.get("https://lowendtalk.com")
        
        # Wait for initial load
        time.sleep(3)
        
        # Wait for Cloudflare bypass
        if not wait_for_cloudflare_bypass(page, max_wait=60):
            return False
        
        # Check if we reached the forum
        time.sleep(2)
        title = page.title if hasattr(page, 'title') else ""
        url = page.url if hasattr(page, 'url') else ""
        
        logger.info(f"📄 Final page title: {title}")
        logger.info(f"🔗 Final URL: {url}")
        
        # Look for forum content
        try:
            discussions = page.eles('.ItemDiscussion')
            if discussions:
                logger.info(f"📋 Found {len(discussions)} discussion items")
                logger.info("🎉 Successfully accessed LowEndTalk forum!")
                return True
            else:
                # Try alternative selectors
                items = page.eles('.Item')
                if items:
                    logger.info(f"📋 Found {len(items)} forum items")
                    logger.info("🎉 Successfully accessed LowEndTalk forum!")
                    return True
                else:
                    logger.warning("⚠️ No forum items found, but page loaded")
                    return True
        except Exception as e:
            logger.warning(f"Could not check forum content: {e}")
            return True  # Still consider successful if we reached the page
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        if browser:
            try:
                browser.quit()
                logger.info("🔒 Browser closed")
            except:
                pass

def main():
    """Main test function"""
    logger.info("🚀 Quick Cloudflare Bypass Test")
    logger.info("=" * 40)
    
    success = test_lowendtalk()
    
    if success:
        logger.info("🎉 Test PASSED! Cloudflare bypass is working.")
        logger.info("You can now run the main crawler with confidence.")
    else:
        logger.error("💥 Test FAILED! Cloudflare bypass needs adjustment.")
        logger.error("Please check your browser configuration or try running in non-headless mode.")
    
    return success

if __name__ == "__main__":
    main()
