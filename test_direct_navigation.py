"""
Test direct navigation to final page without going to first page
"""

import time
import logging
import math

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_page_calculation():
    """Test page calculation logic"""
    logger.info("Testing page calculation logic...")
    
    test_cases = [
        (25, 1, "25 comments -> page 1"),
        (30, 1, "30 comments -> page 1"),
        (31, 2, "31 comments -> page 2"),
        (60, 2, "60 comments -> page 2"),
        (61, 3, "61 comments -> page 3"),
        (90, 3, "90 comments -> page 3"),
        (91, 4, "91 comments -> page 4"),
        (707, 24, "707 comments -> page 24"),
    ]
    
    for comment_count, expected_page, description in test_cases:
        if comment_count <= 30:
            calculated_page = 1
        else:
            calculated_page = math.ceil(comment_count / 30)
        
        if calculated_page == expected_page:
            logger.info(f"PASS: {description}")
        else:
            logger.error(f"FAIL: {description} -> Expected {expected_page}, got {calculated_page}")

def test_url_construction():
    """Test URL construction for direct navigation"""
    logger.info("Testing URL construction...")
    
    test_cases = [
        ("https://lowendtalk.com/discussion/123/test", 25, "https://lowendtalk.com/discussion/123/test"),
        ("https://lowendtalk.com/discussion/123/test", 31, "https://lowendtalk.com/discussion/123/test/p2"),
        ("https://lowendtalk.com/discussion/123/test", 707, "https://lowendtalk.com/discussion/123/test/p24"),
        ("https://lowendtalk.com/discussion/123/test/p5", 707, "https://lowendtalk.com/discussion/123/test/p24"),
    ]
    
    for base_url, comment_count, expected_url in test_cases:
        if comment_count <= 30:
            target_url = base_url
        else:
            final_page = math.ceil(comment_count / 30)
            clean_url = base_url.split('/p')[0]
            target_url = f"{clean_url}/p{final_page}"
        
        if target_url == expected_url:
            logger.info(f"PASS: {comment_count} comments -> {target_url}")
        else:
            logger.error(f"FAIL: {comment_count} comments -> Expected {expected_url}, got {target_url}")

def test_direct_navigation():
    """Test direct navigation with real browser"""
    try:
        from improved_forum_crawler import CrawlerConfig, ForumCrawler, BrowserManager
        
        logger.info("Testing direct navigation...")
        
        config = CrawlerConfig(
            enable_pagination=True,
            comments_per_page=30,
            headless=False
        )
        
        crawler = ForumCrawler(config)
        browser_manager = BrowserManager(config)
        browser_manager.start_browser()
        
        test_page = browser_manager.create_tab()
        if not test_page:
            logger.error("Failed to create test page")
            return False
        
        # Test cases with different comment counts
        test_cases = [
            ("https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price", 25),
            ("https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price", 65),
            ("https://lowendtalk.com/discussion/197419/kimsufi-soyoustart-ovh-rise-new-price", 125),
        ]
        
        for base_url, simulated_comment_count in test_cases:
            logger.info(f"\nTesting with {simulated_comment_count} comments...")
            
            # Calculate target URL
            if simulated_comment_count <= 30:
                target_url = base_url
                expected_page = 1
            else:
                final_page = math.ceil(simulated_comment_count / 30)
                clean_url = base_url.split('/p')[0]
                target_url = f"{clean_url}/p{final_page}"
                expected_page = final_page
            
            logger.info(f"Target URL: {target_url}")
            logger.info(f"Expected page: {expected_page}")
            
            # Navigate directly
            start_time = time.time()
            test_page.get(target_url)
            time.sleep(3)
            navigation_time = time.time() - start_time
            
            # Handle Cloudflare
            browser_manager.handle_cloudflare_verification(test_page, max_wait_time=20)
            
            # Verify we're on the correct page
            current_url = test_page.url
            logger.info(f"Current URL: {current_url}")
            logger.info(f"Navigation time: {navigation_time:.2f} seconds")
            
            if expected_page == 1:
                success = "/p" not in current_url or current_url == target_url
            else:
                success = f"/p{expected_page}" in current_url
            
            if success:
                logger.info(f"SUCCESS: Correctly navigated to page {expected_page}")
            else:
                logger.error(f"FAILED: Expected page {expected_page}, got {current_url}")
            
            # Count comments
            comments = test_page.eles('.Comment')
            logger.info(f"Found {len(comments)} comments on this page")
        
        browser_manager.cleanup()
        return True
        
    except Exception as e:
        logger.error(f"Direct navigation test failed: {e}")
        return False

def test_state_file_check():
    """Check if state file is being updated correctly"""
    logger.info("Checking state file updates...")
    
    try:
        import json
        import os
        
        state_file = "lowendtalk_crawl_state.json"
        if not os.path.exists(state_file):
            logger.warning("State file does not exist")
            return False
        
        with open(state_file, 'r', encoding='utf-8') as f:
            state = json.load(f)
        
        processed_posts = state.get("processed_posts", {})
        logger.info(f"State file has {len(processed_posts)} processed posts")
        
        # Show some examples
        for i, (url, data) in enumerate(list(processed_posts.items())[:3]):
            comment_count = data.get("last_comment_count", 0)
            logger.info(f"  Post {i+1}: {url[:50]}... (Comments: {comment_count})")
        
        return True
        
    except Exception as e:
        logger.error(f"State file check failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Testing Direct Navigation and State Updates")
    logger.info("=" * 50)
    
    # Test 1: Page calculation
    logger.info("Test 1: Page calculation logic")
    test_page_calculation()
    
    # Test 2: URL construction
    logger.info("\nTest 2: URL construction")
    test_url_construction()
    
    # Test 3: State file check
    logger.info("\nTest 3: State file check")
    state_test = test_state_file_check()
    
    # Test 4: Direct navigation
    logger.info("\nTest 4: Direct navigation")
    navigation_test = test_direct_navigation()
    
    # Summary
    logger.info("\nTest Results:")
    logger.info(f"State file check: {'PASS' if state_test else 'FAIL'}")
    logger.info(f"Direct navigation: {'PASS' if navigation_test else 'FAIL'}")
    
    if state_test and navigation_test:
        logger.info("\nAll tests PASSED!")
        logger.info("Direct navigation should now work correctly.")
        logger.info("No more going to first page then last page.")
    else:
        logger.error("\nSome tests FAILED!")
    
    return state_test and navigation_test

if __name__ == "__main__":
    main()
